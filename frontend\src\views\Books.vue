<template>
  <div class="app-container">
    <!-- 顶部导航 -->
    <a-layout-header style="background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); padding: 0 16px; display: flex; align-items: center; justify-content: space-between;">
      <div style="display: flex; align-items: center;">
        <h2 style="margin: 0; color: #333;">我的账本</h2>
      </div>
      <div>
        <a-dropdown>
          <a-button type="text" style="color: #333;">
            <UserOutlined />
            {{ authStore.user?.username }}
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="goToStatistics">
                <BarChartOutlined />
                数据统计
              </a-menu-item>
              <a-menu-item @click="handleLogout">
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </a-layout-header>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <a-spin :spinning="booksStore.loading">
        <div v-if="booksStore.books.length === 0" style="text-align: center; padding: 60px 20px;">
          <a-empty description="暂无账本">
            <a-button type="primary" @click="showCreateModal = true">
              创建第一个账本
            </a-button>
          </a-empty>
        </div>

        <a-row v-else :gutter="[16, 16]">
          <a-col
            v-for="book in booksStore.books"
            :key="book.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
          >
            <a-card
              class="book-card"
              :hoverable="true"
              @click="enterBook(book)"
            >
              <template #actions>
                <EditOutlined @click.stop="editBook(book)" />
                <DeleteOutlined @click.stop="deleteBook(book)" />
              </template>

              <a-card-meta>
                <template #title>
                  <div style="font-size: 18px; font-weight: bold; color: #333;">
                    {{ book.name }}
                  </div>
                </template>
                <template #description>
                  <div style="color: #666; margin-bottom: 12px;">
                    {{ book.description || '暂无描述' }}
                  </div>
                </template>
              </a-card-meta>

              <div class="book-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ book.record_count || 0 }}</div>
                  <div class="stat-label">记录数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">¥{{ formatAmount(book.total_amount || 0) }}</div>
                  <div class="stat-label">累计金额</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-spin>
    </div>

    <!-- 浮动添加按钮 -->
    <div class="floating-btn" @click="showCreateModal = true">
      <PlusOutlined />
    </div>

    <!-- 创建/编辑账本弹窗 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingBook ? '编辑账本' : '创建账本'"
      @ok="handleSaveBook"
      @cancel="handleCancelEdit"
      :confirm-loading="saving"
    >
      <a-form :model="bookForm" layout="vertical">
        <a-form-item label="账本名称" required>
          <a-input
            v-model:value="bookForm.name"
            placeholder="请输入账本名称"
            maxlength="50"
          />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea
            v-model:value="bookForm.description"
            placeholder="请输入账本描述（可选）"
            :rows="3"
            maxlength="200"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  UserOutlined,
  DownOutlined,
  LogoutOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '../stores/auth'
import { useBooksStore } from '../stores/books'

const router = useRouter()
const authStore = useAuthStore()
const booksStore = useBooksStore()

const showCreateModal = ref(false)
const editingBook = ref(null)
const saving = ref(false)
const bookForm = ref({
  name: '',
  description: ''
})

onMounted(() => {
  loadBooks()
})

const loadBooks = async () => {
  try {
    await booksStore.fetchBooks()
  } catch (error) {
    message.error('加载账本失败')
  }
}

const formatAmount = (amount) => {
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const enterBook = (book) => {
  booksStore.setCurrentBook(book)
  router.push(`/books/${book.id}/records`)
}

const editBook = (book) => {
  editingBook.value = book
  bookForm.value = {
    name: book.name,
    description: book.description || ''
  }
  showCreateModal.value = true
}

const deleteBook = (book) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除账本"${book.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await booksStore.deleteBook(book.id)
        message.success('删除成功')
      } catch (error) {
        message.error(error.error || '删除失败')
      }
    }
  })
}

const handleSaveBook = async () => {
  if (!bookForm.value.name.trim()) {
    message.error('请输入账本名称')
    return
  }

  saving.value = true
  try {
    if (editingBook.value) {
      await booksStore.updateBook(editingBook.value.id, bookForm.value)
      message.success('更新成功')
    } else {
      await booksStore.createBook(bookForm.value)
      message.success('创建成功')
    }
    handleCancelEdit()
  } catch (error) {
    message.error(error.error || '保存失败')
  } finally {
    saving.value = false
  }
}

const handleCancelEdit = () => {
  showCreateModal.value = false
  editingBook.value = null
  bookForm.value = {
    name: '',
    description: ''
  }
}

const goToStatistics = () => {
  router.push('/statistics')
}

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    onOk: () => {
      authStore.logout()
      router.push('/login')
    }
  })
}
</script>
