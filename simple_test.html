<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .books {
            margin-top: 20px;
        }
        .book-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>记账系统测试</h1>
        
        <div id="login-section">
            <h2>登录测试</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="password">
            </div>
            <button onclick="testLogin()">登录</button>
            <button onclick="testAPI()">测试API连接</button>
        </div>
        
        <div id="main-section" style="display: none;">
            <h2>账本管理</h2>
            <button onclick="loadBooks()">加载账本</button>
            <button onclick="logout()">退出登录</button>
            <div id="books-container"></div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let token = '';
        
        function showResult(message, type = 'success') {
            const result = document.getElementById('result');
            result.className = 'result ' + type;
            result.innerHTML = message;
        }
        
        async function testAPI() {
            try {
                const response = await fetch('api_direct.php?action=test');
                const data = await response.json();
                showResult('API连接成功: ' + data.message, 'success');
            } catch (error) {
                showResult('API连接失败: ' + error.message, 'error');
            }
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const formData = new FormData();
                formData.append('action', 'login');
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    token = data.data.token;
                    showResult('登录成功! 用户: ' + data.data.user.username, 'success');
                    document.getElementById('login-section').style.display = 'none';
                    document.getElementById('main-section').style.display = 'block';
                    loadBooks();
                } else {
                    showResult('登录失败: ' + data.error, 'error');
                }
            } catch (error) {
                showResult('登录请求失败: ' + error.message, 'error');
            }
        }
        
        async function loadBooks() {
            try {
                const response = await fetch('api_direct.php?action=get_books', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayBooks(data.data);
                    showResult('账本加载成功，共 ' + data.data.length + ' 个账本', 'success');
                } else {
                    showResult('加载账本失败: ' + data.error, 'error');
                }
            } catch (error) {
                showResult('加载账本请求失败: ' + error.message, 'error');
            }
        }
        
        function displayBooks(books) {
            const container = document.getElementById('books-container');
            
            if (books.length === 0) {
                container.innerHTML = '<p>暂无账本</p>';
                return;
            }
            
            let html = '<div class="books"><h3>我的账本</h3>';
            books.forEach(book => {
                html += `
                    <div class="book-item">
                        <h4>${book.name}</h4>
                        <p>描述: ${book.description || '无'}</p>
                        <p>记录数: ${book.record_count || 0}</p>
                        <p>总金额: ¥${parseFloat(book.total_amount || 0).toFixed(2)}</p>
                        <p>创建时间: ${book.created_at}</p>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }
        
        function logout() {
            token = '';
            document.getElementById('login-section').style.display = 'block';
            document.getElementById('main-section').style.display = 'none';
            document.getElementById('books-container').innerHTML = '';
            showResult('已退出登录', 'success');
        }
    </script>
</body>
</html>
