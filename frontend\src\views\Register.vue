<template>
  <div class="login-container">
    <a-card class="login-card" :bordered="false">
      <div class="login-header">
        <h1 class="login-title">用户注册</h1>
        <p class="login-subtitle">创建您的记账账户</p>
      </div>

      <a-form
        :model="form"
        :rules="rules"
        @finish="handleRegister"
        layout="vertical"
        size="large"
      >
        <a-form-item name="username" label="用户名">
          <a-input
            v-model:value="form.username"
            placeholder="请输入用户名"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>

        <a-form-item name="email" label="邮箱">
          <a-input
            v-model:value="form.email"
            placeholder="请输入邮箱地址"
            :prefix="h(MailOutlined)"
          />
        </a-form-item>

        <a-form-item name="password" label="密码">
          <a-input-password
            v-model:value="form.password"
            placeholder="请输入密码（至少6位）"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item name="confirmPassword" label="确认密码">
          <a-input-password
            v-model:value="form.confirmPassword"
            placeholder="请再次输入密码"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
            block
            size="large"
            style="height: 48px; font-size: 16px; border-radius: 8px;"
          >
            注册
          </a-button>
        </a-form-item>

        <div style="text-align: center;">
          <span style="color: #666;">已有账号？</span>
          <router-link to="/login" style="color: #1890ff; text-decoration: none;">
            立即登录
          </router-link>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const form = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = async (rule, value) => {
  if (value && value !== form.value.password) {
    return Promise.reject('两次输入的密码不一致')
  }
  return Promise.resolve()
}

const rules = {
  username: [
    { required: true, message: '请输入用户名' },
    { min: 3, message: '用户名长度不能少于3位' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度不能少于6位' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    { validator: validateConfirmPassword }
  ]
}

const handleRegister = async () => {
  loading.value = true
  try {
    const { confirmPassword, ...registerData } = form.value
    await authStore.register(registerData)
    message.success('注册成功')
    router.push('/books')
  } catch (error) {
    message.error(error.error || '注册失败')
  } finally {
    loading.value = false
  }
}
</script>
