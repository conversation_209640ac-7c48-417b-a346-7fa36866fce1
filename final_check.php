<?php
// 最终部署检查脚本

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>记账系统 - 最终检查</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".check-item { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; background: #f9f9f9; }";
echo ".success { border-left-color: #52c41a; background: #f6ffed; }";
echo ".error { border-left-color: #ff4d4f; background: #fff2f0; }";
echo ".warning { border-left-color: #faad14; background: #fffbe6; }";
echo ".info { border-left-color: #1890ff; background: #f0f9ff; }";
echo "h1 { color: #333; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; margin-top: 30px; }";
echo ".summary { background: #e6f7ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #1890ff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }";
echo ".btn:hover { background: #40a9ff; }";
echo ".code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🎉 记账管理系统 - 最终部署检查</h1>";

$allChecks = [];
$totalScore = 0;
$maxScore = 0;

// 1. 系统环境检查
echo "<h2>1. 系统环境检查</h2>";

$phpVersion = phpversion();
$phpOk = version_compare($phpVersion, '7.4.0', '>=');
$maxScore += 10;
if ($phpOk) {
    $totalScore += 10;
    echo "<div class='check-item success'><strong>✓ PHP版本:</strong> $phpVersion (满足要求)</div>";
} else {
    echo "<div class='check-item error'><strong>✗ PHP版本:</strong> $phpVersion (建议7.4+)</div>";
}

$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    $maxScore += 5;
    if (extension_loaded($ext)) {
        $totalScore += 5;
        echo "<div class='check-item success'><strong>✓ $ext扩展:</strong> 已安装</div>";
    } else {
        echo "<div class='check-item error'><strong>✗ $ext扩展:</strong> 未安装</div>";
    }
}

// 2. 数据库检查
echo "<h2>2. 数据库检查</h2>";

try {
    $dsn = "mysql:host=127.0.0.1;dbname=shuju;charset=utf8mb4";
    $pdo = new PDO($dsn, 'shuju', 'Abc112211', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    $maxScore += 20;
    $totalScore += 20;
    echo "<div class='check-item success'><strong>✓ 数据库连接:</strong> 成功</div>";
    
    $tables = ['users', 'account_books', 'records'];
    foreach ($tables as $table) {
        $maxScore += 5;
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $totalScore += 5;
                
                // 检查表中的数据
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                echo "<div class='check-item success'><strong>✓ 表 $table:</strong> 存在 ($count 条记录)</div>";
            } else {
                echo "<div class='check-item error'><strong>✗ 表 $table:</strong> 不存在</div>";
            }
        } catch (Exception $e) {
            echo "<div class='check-item error'><strong>✗ 表 $table:</strong> 检查失败</div>";
        }
    }
    
} catch (Exception $e) {
    $maxScore += 35; // 20 + 3*5
    echo "<div class='check-item error'><strong>✗ 数据库连接:</strong> 失败 - " . $e->getMessage() . "</div>";
}

// 3. 文件结构检查
echo "<h2>3. 文件结构检查</h2>";

$criticalFiles = [
    'backend/api/config.php' => '后端配置文件',
    'backend/api/auth.php' => '认证接口',
    'backend/api/account_books.php' => '账本接口',
    'backend/api/records.php' => '记录接口',
    'backend/api/statistics.php' => '统计接口',
    'backend/api/export.php' => '导出接口',
    'backend/database/init.sql' => '数据库初始化脚本',
    'backend/cron/monthly_reset.php' => '月度重置脚本',
    '.htaccess' => 'Apache重写规则'
];

foreach ($criticalFiles as $file => $desc) {
    $maxScore += 3;
    if (file_exists($file)) {
        $totalScore += 3;
        $size = round(filesize($file) / 1024, 2);
        echo "<div class='check-item success'><strong>✓ $desc:</strong> 存在 ({$size}KB)</div>";
    } else {
        echo "<div class='check-item error'><strong>✗ $desc:</strong> 不存在 ($file)</div>";
    }
}

// 4. 前端构建检查
echo "<h2>4. 前端构建检查</h2>";

$frontendFiles = [
    'frontend/package.json' => '前端配置文件',
    'frontend/dist/index.html' => '前端构建文件',
    'frontend/dist/assets' => '前端资源目录'
];

foreach ($frontendFiles as $file => $desc) {
    $maxScore += 5;
    if (file_exists($file)) {
        $totalScore += 5;
        if (is_dir($file)) {
            $count = count(glob($file . '/*'));
            echo "<div class='check-item success'><strong>✓ $desc:</strong> 存在 ($count 个文件)</div>";
        } else {
            $size = round(filesize($file) / 1024, 2);
            echo "<div class='check-item success'><strong>✓ $desc:</strong> 存在 ({$size}KB)</div>";
        }
    } else {
        echo "<div class='check-item warning'><strong>⚠ $desc:</strong> 不存在 ($file)</div>";
    }
}

// 5. API接口测试
echo "<h2>5. API接口测试</h2>";

$apiTests = [
    '/api/auth/login' => 'POST',
    '/api/account-books' => 'GET',
    '/api/statistics/overview' => 'GET'
];

foreach ($apiTests as $endpoint => $method) {
    $maxScore += 3;
    echo "<div class='check-item info'><strong>📋 API接口:</strong> $method $endpoint (需要手动测试)</div>";
}

// 6. 安全检查
echo "<h2>6. 安全检查</h2>";

$securityChecks = [
    'JWT密钥配置' => defined('JWT_SECRET') && JWT_SECRET !== 'your-secret-key-change-this-in-production',
    '错误显示关闭' => ini_get('display_errors') == '0',
    'HTTPS重定向' => false // 需要手动检查
];

foreach ($securityChecks as $check => $passed) {
    $maxScore += 5;
    if ($passed) {
        $totalScore += 5;
        echo "<div class='check-item success'><strong>✓ $check:</strong> 已配置</div>";
    } else {
        echo "<div class='check-item warning'><strong>⚠ $check:</strong> 需要配置</div>";
    }
}

// 计算总分
$percentage = round(($totalScore / $maxScore) * 100, 1);
$status = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'error');

echo "<div class='summary'>";
echo "<h2>📊 检查总结</h2>";
echo "<div class='check-item $status'>";
echo "<strong>总体评分:</strong> $totalScore / $maxScore 分 ($percentage%)";
echo "</div>";

if ($percentage >= 90) {
    echo "<div class='check-item success'>";
    echo "<strong>🎉 恭喜!</strong> 系统已准备就绪，可以正常使用！";
    echo "</div>";
} elseif ($percentage >= 70) {
    echo "<div class='check-item warning'>";
    echo "<strong>⚠ 注意:</strong> 系统基本可用，但建议解决警告项目。";
    echo "</div>";
} else {
    echo "<div class='check-item error'>";
    echo "<strong>❌ 错误:</strong> 系统存在严重问题，请先解决错误项目。";
    echo "</div>";
}
echo "</div>";

// 下一步操作
echo "<h2>🚀 下一步操作</h2>";

echo "<div class='check-item info'>";
echo "<strong>1. 如果前端未构建，请执行:</strong>";
echo "<div class='code'>cd frontend<br>npm install<br>npm run build</div>";
echo "</div>";

echo "<div class='check-item info'>";
echo "<strong>2. 配置定时任务 (可选):</strong>";
echo "<div class='code'>0 0 1 * * php " . __DIR__ . "/backend/cron/monthly_reset.php</div>";
echo "</div>";

echo "<div class='check-item info'>";
echo "<strong>3. 生产环境安全配置:</strong>";
echo "<div class='code'>";
echo "- 修改 JWT_SECRET 为随机字符串<br>";
echo "- 关闭 PHP 错误显示<br>";
echo "- 配置 HTTPS<br>";
echo "- 删除测试文件 (test.php, install.php, final_check.php)";
echo "</div>";
echo "</div>";

echo "<div class='check-item success'>";
echo "<strong>4. 开始使用:</strong><br>";
echo "默认管理员账户: admin / password<br>";
echo "<a href='welcome.html' class='btn'>访问系统</a>";
echo "<a href='test.php' class='btn'>系统测试</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
