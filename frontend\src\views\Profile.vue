<template>
  <div class="app-container">
    <!-- 顶部导航 -->
    <a-layout-header style="background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); padding: 0 16px; display: flex; align-items: center;">
      <a-button type="text" @click="goBack" style="margin-right: 8px;">
        <ArrowLeftOutlined />
      </a-button>
      <h2 style="margin: 0; color: #333;">个人资料</h2>
    </a-layout-header>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <a-card title="用户信息" style="margin-bottom: 16px;">
        <a-descriptions :column="1">
          <a-descriptions-item label="用户名">
            {{ authStore.user?.username }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱">
            {{ authStore.user?.email }}
          </a-descriptions-item>
          <a-descriptions-item label="注册时间">
            {{ formatDateTime(authStore.user?.created_at) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="应用信息">
        <a-descriptions :column="1">
          <a-descriptions-item label="应用名称">
            记账管理系统
          </a-descriptions-item>
          <a-descriptions-item label="版本">
            v1.0.0
          </a-descriptions-item>
          <a-descriptions-item label="开发者">
            AI Assistant
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <div style="margin-top: 24px; text-align: center;">
        <a-button type="primary" danger @click="handleLogout">
          退出登录
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Modal } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '../stores/auth'
import { formatDateTime } from '../utils/date'

const router = useRouter()
const authStore = useAuthStore()

const goBack = () => {
  router.push('/books')
}

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    onOk: () => {
      authStore.logout()
      router.push('/login')
    }
  })
}
</script>
