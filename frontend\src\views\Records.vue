<template>
  <div class="app-container">
    <!-- 顶部导航 -->
    <a-layout-header style="background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); padding: 0 16px; display: flex; align-items: center; justify-content: space-between;">
      <div style="display: flex; align-items: center;">
        <a-button type="text" @click="goBack" style="margin-right: 8px;">
          <ArrowLeftOutlined />
        </a-button>
        <h2 style="margin: 0; color: #333;">{{ currentBook?.name || '记账记录' }}</h2>
      </div>
      <div>
        <a-dropdown>
          <a-button type="text">
            <MoreOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleMonthlyReset">
                <ReloadOutlined />
                月度重置
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </a-layout-header>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <a-spin :spinning="recordsStore.loading">
        <div v-if="recordsStore.records.length === 0" style="text-align: center; padding: 60px 20px;">
          <a-empty description="暂无记录">
            <a-button type="primary" @click="showCreateModal = true">
              添加第一条记录
            </a-button>
          </a-empty>
        </div>

        <div v-else>
          <div
            v-for="record in recordsStore.records"
            :key="record.id"
            class="record-card"
            :class="{ completed: record.is_completed }"
          >
            <a-card :bordered="false">
              <div class="record-header">
                <div style="display: flex; align-items: center;">
                  <a-checkbox
                    :checked="record.is_completed"
                    @change="toggleRecord(record)"
                    :disabled="record.is_locked && !isCurrentMonth(record.completed_month)"
                  />
                  <span style="margin-left: 8px; font-weight: bold; font-size: 16px;">
                    {{ record.name }}
                  </span>
                </div>
                <div>
                  <a-button type="text" size="small" @click="editRecord(record)">
                    <EditOutlined />
                  </a-button>
                  <a-button type="text" size="small" @click="deleteRecord(record)" danger>
                    <DeleteOutlined />
                  </a-button>
                </div>
              </div>

              <div class="record-content">
                <div class="record-item">
                  <span class="record-label">日期:</span>
                  <span class="record-value">{{ formatDate(record.date) }}</span>
                </div>
                <div class="record-item">
                  <span class="record-label">金额:</span>
                  <span class="record-value amount-highlight">¥{{ formatAmount(record.amount) }}</span>
                </div>
                <div class="record-item">
                  <span class="record-label">每月金额:</span>
                  <span class="record-value">¥{{ formatAmount(record.monthly_amount) }}</span>
                </div>
                <div class="record-item">
                  <span class="record-label">续期时间:</span>
                  <span class="record-value">{{ record.renewal_time }}</span>
                </div>
                <div class="record-item">
                  <span class="record-label">续期金额:</span>
                  <span class="record-value">¥{{ formatAmount(record.renewal_amount) }}</span>
                </div>
                <div class="record-item" v-if="record.remark">
                  <span class="record-label">备注:</span>
                  <span class="record-value">{{ record.remark }}</span>
                </div>
              </div>

              <div class="accumulated-amount">
                累计金额: ¥{{ formatAmount(record.accumulated_amount) }}
                <span v-if="record.is_locked" style="margin-left: 8px; color: #ff7875; font-size: 12px;">
                  (已锁定)
                </span>
              </div>
            </a-card>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 浮动添加按钮 -->
    <div class="floating-btn" @click="showCreateModal = true">
      <PlusOutlined />
    </div>

    <!-- 创建/编辑记录弹窗 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingRecord ? '编辑记录' : '添加记录'"
      @ok="handleSaveRecord"
      @cancel="handleCancelEdit"
      :confirm-loading="saving"
      width="90%"
      :style="{ maxWidth: '500px' }"
    >
      <a-form :model="recordForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="日期" required>
              <a-date-picker
                v-model:value="recordForm.date"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="姓名" required>
              <a-input
                v-model:value="recordForm.name"
                placeholder="请输入姓名"
                maxlength="50"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="金额" required>
              <a-input-number
                v-model:value="recordForm.amount"
                placeholder="0.00"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="每月金额" required>
              <a-input-number
                v-model:value="recordForm.monthly_amount"
                placeholder="0.00"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="续期时间" required>
              <a-select
                v-model:value="recordForm.renewal_time"
                placeholder="请选择续期时间"
              >
                <a-select-option value="二个月">二个月</a-select-option>
                <a-select-option value="三个月">三个月</a-select-option>
                <a-select-option value="六个月">六个月</a-select-option>
                <a-select-option value="永久">永久</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="续期金额" required>
              <a-input-number
                v-model:value="recordForm.renewal_amount"
                placeholder="0.00"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="备注">
          <a-textarea
            v-model:value="recordForm.remark"
            placeholder="请输入备注（可选）"
            :rows="2"
            maxlength="200"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ArrowLeftOutlined,
  MoreOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { useBooksStore } from '../stores/books'
import { useRecordsStore } from '../stores/records'
import { formatDate, getCurrentDate, isCurrentMonth } from '../utils/date'

const route = useRoute()
const router = useRouter()
const booksStore = useBooksStore()
const recordsStore = useRecordsStore()

const bookId = computed(() => route.params.id)
const currentBook = computed(() => booksStore.currentBook)

const showCreateModal = ref(false)
const editingRecord = ref(null)
const saving = ref(false)
const recordForm = ref({
  date: getCurrentDate(),
  name: '',
  amount: null,
  monthly_amount: null,
  renewal_time: '',
  renewal_amount: null,
  remark: ''
})

onMounted(() => {
  loadRecords()
})

const loadRecords = async () => {
  try {
    await recordsStore.fetchRecords(bookId.value)
  } catch (error) {
    message.error('加载记录失败')
  }
}

const formatAmount = (amount) => {
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const goBack = () => {
  router.push('/books')
}

const toggleRecord = async (record) => {
  try {
    await recordsStore.toggleRecord(bookId.value, record.id)
    message.success('状态更新成功')
  } catch (error) {
    message.error(error.error || '状态更新失败')
  }
}

const editRecord = (record) => {
  editingRecord.value = record
  recordForm.value = {
    date: record.date,
    name: record.name,
    amount: Number(record.amount),
    monthly_amount: Number(record.monthly_amount),
    renewal_time: record.renewal_time,
    renewal_amount: Number(record.renewal_amount),
    remark: record.remark || ''
  }
  showCreateModal.value = true
}

const deleteRecord = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除记录"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await recordsStore.deleteRecord(bookId.value, record.id)
        message.success('删除成功')
      } catch (error) {
        message.error(error.error || '删除失败')
      }
    }
  })
}

const handleSaveRecord = async () => {
  // 验证必填字段
  if (!recordForm.value.date || !recordForm.value.name ||
      recordForm.value.amount === null || recordForm.value.monthly_amount === null ||
      !recordForm.value.renewal_time || recordForm.value.renewal_amount === null) {
    message.error('请填写所有必填字段')
    return
  }

  saving.value = true
  try {
    if (editingRecord.value) {
      await recordsStore.updateRecord(bookId.value, editingRecord.value.id, recordForm.value)
      message.success('更新成功')
    } else {
      await recordsStore.createRecord(bookId.value, recordForm.value)
      message.success('添加成功')
    }
    handleCancelEdit()
  } catch (error) {
    message.error(error.error || '保存失败')
  } finally {
    saving.value = false
  }
}

const handleCancelEdit = () => {
  showCreateModal.value = false
  editingRecord.value = null
  recordForm.value = {
    date: getCurrentDate(),
    name: '',
    amount: null,
    monthly_amount: null,
    renewal_time: '',
    renewal_amount: null,
    remark: ''
  }
}

const handleMonthlyReset = () => {
  Modal.confirm({
    title: '确认月度重置',
    content: '确定要执行月度重置吗？这将取消所有未锁定记录的完成状态，并锁定上月记录。',
    onOk: async () => {
      try {
        await recordsStore.resetMonthlyRecords()
        await loadRecords() // 重新加载记录
        message.success('月度重置完成')
      } catch (error) {
        message.error(error.error || '月度重置失败')
      }
    }
  })
}
</script>
