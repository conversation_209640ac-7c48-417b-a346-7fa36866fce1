import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

export const formatDate = (date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}

export const formatDateTime = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

export const getCurrentDate = () => {
  return dayjs().format('YYYY-MM-DD')
}

export const getCurrentMonth = () => {
  return dayjs().format('YYYY-MM')
}

export const isCurrentMonth = (date) => {
  return dayjs(date).format('YYYY-MM') === getCurrentMonth()
}

export const addMonths = (date, months) => {
  return dayjs(date).add(months, 'month').format('YYYY-MM-DD')
}

export const getRenewalDate = (renewalTime) => {
  const now = dayjs()
  
  switch (renewalTime) {
    case '二个月':
      return now.add(2, 'month').format('YYYY-MM-DD')
    case '三个月':
      return now.add(3, 'month').format('YYYY-MM-DD')
    case '六个月':
      return now.add(6, 'month').format('YYYY-MM-DD')
    case '永久':
      return '永久'
    default:
      return now.format('YYYY-MM-DD')
  }
}

export const isFirstDayOfMonth = () => {
  return dayjs().date() === 1
}

export default {
  formatDate,
  formatDateTime,
  getCurrentDate,
  getCurrentMonth,
  isCurrentMonth,
  addMonths,
  getRenewalDate,
  isFirstDayOfMonth
}
