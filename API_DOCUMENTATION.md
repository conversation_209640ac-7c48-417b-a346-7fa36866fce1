# API 接口文档

## 基础信息

- **Base URL**: `/api`
- **认证方式**: JWT <PERSON>er <PERSON>ken
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 认证接口

### 用户注册
```
POST /api/auth/register
```

**请求参数:**
```json
{
  "username": "string",     // 用户名，3-50字符
  "email": "string",        // 邮箱地址
  "password": "string"      // 密码，最少6位
}
```

**响应示例:**
```json
{
  "message": "注册成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

### 用户登录
```
POST /api/auth/login
```

**请求参数:**
```json
{
  "username": "string",     // 用户名或邮箱
  "password": "string"      // 密码
}
```

**响应示例:**
```json
{
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

## 账本管理

### 获取账本列表
```
GET /api/account-books
Authorization: Bearer {token}
```

**响应示例:**
```json
{
  "message": "Success",
  "data": [
    {
      "id": 1,
      "name": "默认账本",
      "description": "系统自动创建的默认账本",
      "record_count": 5,
      "total_amount": "15000.00",
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00"
    }
  ]
}
```

### 创建账本
```
POST /api/account-books
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "name": "string",         // 账本名称，必填
  "description": "string"   // 账本描述，可选
}
```

### 更新账本
```
PUT /api/account-books/{id}
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "name": "string",         // 账本名称，必填
  "description": "string"   // 账本描述，可选
}
```

### 删除账本
```
DELETE /api/account-books/{id}
Authorization: Bearer {token}
```

## 记录管理

### 获取记录列表
```
GET /api/records/{bookId}
Authorization: Bearer {token}
```

**响应示例:**
```json
{
  "message": "Success",
  "data": [
    {
      "id": 1,
      "date": "2024-01-30",
      "name": "荷花",
      "amount": "30000.00",
      "monthly_amount": "3000.00",
      "renewal_time": "三个月",
      "renewal_amount": "6000.00",
      "remark": "备注信息",
      "accumulated_amount": "3000.00",
      "is_completed": true,
      "completed_month": "2024-01",
      "is_locked": false,
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00"
    }
  ]
}
```

### 创建记录
```
POST /api/records/{bookId}
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "date": "2024-01-30",           // 日期，YYYY-MM-DD格式
  "name": "荷花",                 // 姓名
  "amount": 30000.00,             // 金额
  "monthly_amount": 3000.00,      // 每月金额
  "renewal_time": "三个月",        // 续期时间：二个月/三个月/六个月/永久
  "renewal_amount": 6000.00,      // 续期金额
  "remark": "备注信息"            // 备注，可选
}
```

### 更新记录
```
PUT /api/records/{bookId}/{recordId}
Authorization: Bearer {token}
```

**请求参数:** 同创建记录

### 删除记录
```
DELETE /api/records/{bookId}/{recordId}
Authorization: Bearer {token}
```

### 切换完成状态
```
POST /api/records/{bookId}/{recordId}/toggle
Authorization: Bearer {token}
```

**响应说明:**
- 标记完成：累计金额增加每月金额
- 取消完成：如果是当月操作，累计金额减少每月金额
- 已锁定记录：只能在当月修改状态

### 月度重置
```
POST /api/records/reset-monthly
Authorization: Bearer {token}
```

**功能说明:**
- 锁定上个月已完成的记录
- 重置当月所有未锁定记录的完成状态

## 错误响应

所有接口在出错时返回统一格式：

```json
{
  "error": "错误信息描述"
}
```

**常见错误码:**
- `400` - 请求参数错误
- `401` - 未授权或token无效
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 业务规则

### 续期时间规则
- **二个月**: 到期后自动延长2个月
- **三个月**: 到期后自动延长3个月  
- **六个月**: 到期后自动延长6个月
- **永久**: 永不到期

### 累计金额规则
1. **标记完成**: 累计金额 += 每月金额
2. **取消完成**: 
   - 当月操作: 累计金额 -= 每月金额
   - 非当月: 不允许操作（已锁定）
3. **月度锁定**: 过月后记录被锁定，累计金额不可逆转

### 权限控制
- 用户只能访问自己的账本和记录
- 账本所有者才能管理账本下的记录
- 不能删除用户的最后一个账本

## 数据验证

### 字段限制
- `username`: 3-50字符，字母数字下划线
- `email`: 有效邮箱格式
- `password`: 最少6位
- `name`: 1-100字符
- `amount`: 大于等于0的数字，最多2位小数
- `date`: YYYY-MM-DD格式
- `renewal_time`: 只能是指定的4个选项

### 安全措施
- JWT token 7天有效期
- 密码使用 bcrypt 哈希
- SQL 预处理防注入
- XSS 过滤
- CORS 跨域控制

## 使用示例

### JavaScript 调用示例

```javascript
// 登录
const login = async (username, password) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ username, password })
  });
  return response.json();
};

// 获取账本列表
const getBooks = async (token) => {
  const response = await fetch('/api/account-books', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 创建记录
const createRecord = async (token, bookId, recordData) => {
  const response = await fetch(`/api/records/${bookId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(recordData)
  });
  return response.json();
};
```

### cURL 调用示例

```bash
# 登录
curl -X POST /api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 获取账本列表
curl -X GET /api/account-books \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建记录
curl -X POST /api/records/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "date": "2024-01-30",
    "name": "测试用户",
    "amount": 1000.00,
    "monthly_amount": 100.00,
    "renewal_time": "三个月",
    "renewal_amount": 300.00,
    "remark": "测试记录"
  }'
```
