<template>
  <div class="login-container">
    <a-card class="login-card" :bordered="false">
      <div class="login-header">
        <h1 class="login-title">记账管理</h1>
        <p class="login-subtitle">简单高效的多用户记账系统</p>
      </div>

      <a-form
        :model="form"
        :rules="rules"
        @finish="handleLogin"
        layout="vertical"
        size="large"
      >
        <a-form-item name="username" label="用户名/邮箱">
          <a-input
            v-model:value="form.username"
            placeholder="请输入用户名或邮箱"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>

        <a-form-item name="password" label="密码">
          <a-input-password
            v-model:value="form.password"
            placeholder="请输入密码"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            :loading="loading"
            block
            size="large"
            style="height: 48px; font-size: 16px; border-radius: 8px;"
          >
            登录
          </a-button>
        </a-form-item>

        <div style="text-align: center;">
          <span style="color: #666;">还没有账号？</span>
          <router-link to="/register" style="color: #1890ff; text-decoration: none;">
            立即注册
          </router-link>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const form = ref({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名或邮箱' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度不能少于6位' }
  ]
}

const handleLogin = async () => {
  loading.value = true
  try {
    await authStore.login(form.value)
    message.success('登录成功')
    router.push('/books')
  } catch (error) {
    message.error(error.error || '登录失败')
  } finally {
    loading.value = false
  }
}
</script>
