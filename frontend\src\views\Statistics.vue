<template>
  <div class="app-container">
    <!-- 顶部导航 -->
    <a-layout-header style="background: rgba(255,255,255,0.9); backdrop-filter: blur(10px); padding: 0 16px; display: flex; align-items: center; justify-content: space-between;">
      <div style="display: flex; align-items: center;">
        <a-button type="text" @click="goBack" style="margin-right: 8px;">
          <ArrowLeftOutlined />
        </a-button>
        <h2 style="margin: 0; color: #333;">数据统计</h2>
      </div>
      <div>
        <a-dropdown>
          <a-button type="text">
            <DownloadOutlined />
            导出
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="exportCSV">
                <FileExcelOutlined />
                导出CSV
              </a-menu-item>
              <a-menu-item @click="exportJSON">
                <FileTextOutlined />
                导出JSON
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </a-layout-header>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <a-spin :spinning="loading">
        <!-- 总览统计 -->
        <a-card title="总览统计" style="margin-bottom: 16px;">
          <a-row :gutter="16">
            <a-col :xs="12" :sm="6">
              <a-statistic
                title="总账本数"
                :value="overviewStats?.total_books || 0"
                :value-style="{ color: '#3f8600' }"
              />
            </a-col>
            <a-col :xs="12" :sm="6">
              <a-statistic
                title="总记录数"
                :value="overviewStats?.total_records || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="12" :sm="6">
              <a-statistic
                title="已完成"
                :value="overviewStats?.completed_records || 0"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :xs="12" :sm="6">
              <a-statistic
                title="累计金额"
                :value="overviewStats?.total_accumulated || 0"
                :precision="2"
                prefix="¥"
                :value-style="{ color: '#f5222d' }"
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 本月统计 -->
        <a-card title="本月统计" style="margin-bottom: 16px;">
          <a-row :gutter="16">
            <a-col :xs="8" :sm="8">
              <a-statistic
                title="本月记录"
                :value="monthStats?.month_records || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="8" :sm="8">
              <a-statistic
                title="本月完成"
                :value="monthStats?.month_completed || 0"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :xs="8" :sm="8">
              <a-statistic
                title="本月收入"
                :value="monthStats?.month_income || 0"
                :precision="2"
                prefix="¥"
                :value-style="{ color: '#f5222d' }"
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 续期时间分布 -->
        <a-card title="续期时间分布" style="margin-bottom: 16px;">
          <div v-if="renewalDistribution && renewalDistribution.length > 0">
            <div
              v-for="item in renewalDistribution"
              :key="item.renewal_time"
              style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; padding: 8px; background: #f5f5f5; border-radius: 6px;"
            >
              <span style="font-weight: 500;">{{ item.renewal_time }}</span>
              <div style="display: flex; align-items: center; gap: 12px;">
                <span>{{ item.count }} 条</span>
                <span style="color: #666;">{{ item.percentage }}%</span>
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无数据" />
        </a-card>

        <!-- 月度趋势 -->
        <a-card title="月度趋势" style="margin-bottom: 16px;">
          <div v-if="monthlyTrends && monthlyTrends.length > 0">
            <div class="trend-chart">
              <div
                v-for="month in monthlyTrends.slice(-6)"
                :key="month.month"
                class="trend-item"
              >
                <div class="trend-month">{{ month.month_name }}</div>
                <div class="trend-bar">
                  <div
                    class="trend-progress"
                    :style="{ width: month.completion_rate + '%' }"
                  ></div>
                </div>
                <div class="trend-stats">
                  <div>完成率: {{ month.completion_rate }}%</div>
                  <div>收入: ¥{{ formatAmount(month.income) }}</div>
                </div>
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无数据" />
        </a-card>

        <!-- 账本活跃度 -->
        <a-card title="账本活跃度">
          <div v-if="bookActivity && bookActivity.length > 0">
            <div
              v-for="book in bookActivity"
              :key="book.book_name"
              style="margin-bottom: 16px; padding: 12px; border: 1px solid #e8e8e8; border-radius: 8px;"
            >
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <h4 style="margin: 0;">{{ book.book_name }}</h4>
                <span style="color: #666; font-size: 12px;">
                  {{ formatDateTime(book.last_activity) }}
                </span>
              </div>
              <a-row :gutter="16">
                <a-col :span="6">
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #1890ff;">
                      {{ book.record_count }}
                    </div>
                    <div style="font-size: 12px; color: #666;">记录数</div>
                  </div>
                </a-col>
                <a-col :span="6">
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #52c41a;">
                      {{ book.completed_count }}
                    </div>
                    <div style="font-size: 12px; color: #666;">已完成</div>
                  </div>
                </a-col>
                <a-col :span="12">
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #f5222d;">
                      ¥{{ formatAmount(book.total_accumulated) }}
                    </div>
                    <div style="font-size: 12px; color: #666;">累计金额</div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
          <a-empty v-else description="暂无数据" />
        </a-card>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import api from '../utils/api'
import { formatDateTime } from '../utils/date'

const router = useRouter()

const loading = ref(false)
const overviewStats = ref(null)
const monthStats = ref(null)
const renewalDistribution = ref([])
const monthlyTrends = ref([])
const bookActivity = ref([])

onMounted(() => {
  loadStatistics()
})

const loadStatistics = async () => {
  loading.value = true
  try {
    // 加载总览统计
    const overviewResponse = await api.get('/statistics/overview')
    const data = overviewResponse.data.data
    overviewStats.value = data.overview
    monthStats.value = data.current_month
    renewalDistribution.value = data.renewal_distribution

    // 加载月度趋势
    const monthlyResponse = await api.get('/statistics/monthly')
    monthlyTrends.value = monthlyResponse.data.data

    // 加载趋势数据
    const trendsResponse = await api.get('/statistics/trends')
    bookActivity.value = trendsResponse.data.data.book_activity

  } catch (error) {
    message.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const formatAmount = (amount) => {
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const exportCSV = () => {
  const token = localStorage.getItem('token')
  const url = `/api/export/csv`
  window.open(`${url}?token=${token}`, '_blank')
}

const exportJSON = () => {
  const token = localStorage.getItem('token')
  const url = `/api/export/json`
  window.open(`${url}?token=${token}`, '_blank')
}

const goBack = () => {
  router.push('/books')
}
</script>

<style scoped>
.trend-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
}

.trend-month {
  min-width: 80px;
  font-size: 12px;
  font-weight: 500;
}

.trend-bar {
  flex: 1;
  height: 8px;
  background: #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.trend-progress {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  transition: width 0.3s ease;
}

.trend-stats {
  min-width: 120px;
  font-size: 11px;
  color: #666;
  text-align: right;
}

@media (max-width: 768px) {
  .trend-item {
    flex-direction: column;
    align-items: stretch;
  }

  .trend-month {
    min-width: auto;
    text-align: center;
  }

  .trend-stats {
    min-width: auto;
    text-align: center;
  }
}
</style>
