import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useRecordsStore = defineStore('records', () => {
  const records = ref([])
  const loading = ref(false)

  const fetchRecords = async (bookId) => {
    loading.value = true
    try {
      const response = await api.get(`/records/${bookId}`)
      records.value = response.data.data
    } catch (error) {
      throw error.response?.data || { error: '获取记录失败' }
    } finally {
      loading.value = false
    }
  }

  const createRecord = async (bookId, recordData) => {
    try {
      const response = await api.post(`/records/${bookId}`, recordData)
      records.value.unshift(response.data.data)
      return response.data
    } catch (error) {
      throw error.response?.data || { error: '创建记录失败' }
    }
  }

  const updateRecord = async (bookId, recordId, recordData) => {
    try {
      const response = await api.put(`/records/${bookId}/${recordId}`, recordData)
      const index = records.value.findIndex(record => record.id === recordId)
      if (index !== -1) {
        records.value[index] = response.data.data
      }
      return response.data
    } catch (error) {
      throw error.response?.data || { error: '更新记录失败' }
    }
  }

  const deleteRecord = async (bookId, recordId) => {
    try {
      await api.delete(`/records/${bookId}/${recordId}`)
      records.value = records.value.filter(record => record.id !== recordId)
    } catch (error) {
      throw error.response?.data || { error: '删除记录失败' }
    }
  }

  const toggleRecord = async (bookId, recordId) => {
    try {
      const response = await api.post(`/records/${bookId}/${recordId}/toggle`)
      const index = records.value.findIndex(record => record.id === recordId)
      if (index !== -1) {
        records.value[index] = response.data.data
      }
      return response.data
    } catch (error) {
      throw error.response?.data || { error: '状态更新失败' }
    }
  }

  const resetMonthlyRecords = async () => {
    try {
      await api.post('/records/reset-monthly')
      // 重新获取当前记录
      return true
    } catch (error) {
      throw error.response?.data || { error: '月度重置失败' }
    }
  }

  return {
    records,
    loading,
    fetchRecords,
    createRecord,
    updateRecord,
    deleteRecord,
    toggleRecord,
    resetMonthlyRecords
  }
})
