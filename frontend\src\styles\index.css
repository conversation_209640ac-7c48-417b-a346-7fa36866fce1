/* 全局样式 */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content-wrapper {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 12px;
  }
  
  .ant-card {
    margin-bottom: 12px;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-btn {
    height: 44px;
    font-size: 16px;
  }
  
  .ant-input {
    height: 44px;
    font-size: 16px;
  }
  
  .ant-select {
    height: 44px;
  }
  
  .ant-select .ant-select-selector {
    height: 44px !important;
    padding: 8px 12px !important;
  }
  
  .ant-select .ant-select-selection-item {
    line-height: 28px !important;
  }
}

/* 记录卡片样式 */
.record-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.record-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.record-card.completed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 14px;
}

@media (max-width: 480px) {
  .record-content {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
}

.record-label {
  color: #666;
  font-weight: 500;
}

.record-value {
  color: #333;
  font-weight: 600;
}

.amount-highlight {
  color: #f5222d;
  font-size: 16px;
  font-weight: bold;
}

.accumulated-amount {
  color: #52c41a;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  padding: 8px;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 8px;
  margin-top: 12px;
}

/* 账本卡片样式 */
.book-card {
  border-radius: 16px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.book-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 浮动按钮 */
.floating-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
}

.login-subtitle {
  color: #666;
  font-size: 14px;
}
