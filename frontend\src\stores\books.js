import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../utils/api'

export const useBooksStore = defineStore('books', () => {
  const books = ref([])
  const currentBook = ref(null)
  const loading = ref(false)

  const fetchBooks = async () => {
    loading.value = true
    try {
      const response = await api.get('/account-books')
      books.value = response.data.data
    } catch (error) {
      throw error.response?.data || { error: '获取账本失败' }
    } finally {
      loading.value = false
    }
  }

  const createBook = async (bookData) => {
    try {
      const response = await api.post('/account-books', bookData)
      books.value.unshift(response.data.data)
      return response.data
    } catch (error) {
      throw error.response?.data || { error: '创建账本失败' }
    }
  }

  const updateBook = async (bookId, bookData) => {
    try {
      const response = await api.put(`/account-books/${bookId}`, bookData)
      const index = books.value.findIndex(book => book.id === bookId)
      if (index !== -1) {
        books.value[index] = response.data.data
      }
      return response.data
    } catch (error) {
      throw error.response?.data || { error: '更新账本失败' }
    }
  }

  const deleteBook = async (bookId) => {
    try {
      await api.delete(`/account-books/${bookId}`)
      books.value = books.value.filter(book => book.id !== bookId)
    } catch (error) {
      throw error.response?.data || { error: '删除账本失败' }
    }
  }

  const setCurrentBook = (book) => {
    currentBook.value = book
  }

  return {
    books,
    currentBook,
    loading,
    fetchBooks,
    createBook,
    updateBook,
    deleteBook,
    setCurrentBook
  }
})
