<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    require_once 'backend/api/config.php';

    $action = $_GET['action'] ?? $_POST['action'] ?? 'test';

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'API连接正常',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;

        case 'login':
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名和密码不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 查找用户
            $stmt = $pdo->prepare("SELECT id, username, email, password FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                http_response_code(401);
                echo json_encode(['error' => '用户名或密码错误']);
                exit;
            }

            // 生成JWT
            $payload = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ]
                ]
            ]);
            break;

        case 'register':
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($email) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名、邮箱和密码不能为空']);
                exit;
            }

            if (strlen($password) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '密码至少需要6位']);
                exit;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱格式不正确']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 检查用户名是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '用户名已存在']);
                exit;
            }

            // 检查邮箱是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱已被注册']);
                exit;
            }

            // 创建新用户
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute([$username, $email, $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            // 生成JWT
            $payload = [
                'user_id' => $userId,
                'username' => $username,
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $userId,
                        'username' => $username,
                        'email' => $email
                    ]
                ]
            ]);
            break;

        case 'create_admin':
            $db = new Database();
            $pdo = $db->getConnection();

            // 删除现有admin用户
            $stmt = $pdo->prepare("DELETE FROM users WHERE username = 'admin'");
            $stmt->execute();

            // 创建新的admin用户
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            echo json_encode([
                'success' => true,
                'message' => 'admin用户创建成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => 'admin',
                    'password' => 'password'
                ]
            ]);
            break;

        case 'get_books':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            $stmt = $pdo->prepare("
                SELECT ab.*,
                       COUNT(r.id) as record_count,
                       COALESCE(SUM(r.accumulated_amount), 0) as total_amount
                FROM account_books ab
                LEFT JOIN records r ON ab.id = r.account_book_id
                WHERE ab.user_id = ?
                GROUP BY ab.id
                ORDER BY ab.created_at DESC
            ");
            $stmt->execute([$userId]);
            $books = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $books
            ]);
            break;

        case 'get_records':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_GET['book_id'] ?? '';
            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 获取记录
            $stmt = $pdo->prepare("
                SELECT * FROM records
                WHERE account_book_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$bookId]);
            $records = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $records
            ]);
            break;

        case 'add_record':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';

            if (empty($bookId) || empty($date) || empty($name) || empty($amount)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 初始累计金额为0，只有标记完成时才累计
            $accumulatedAmount = 0;

            // 插入记录
            $stmt = $pdo->prepare("
                INSERT INTO records (account_book_id, date, name, amount, monthly_amount, renewal_time, renewal_amount, accumulated_amount, remark, is_completed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
            ");
            $stmt->execute([
                $bookId, $date, $name, $amount, $monthlyAmount,
                $renewalTime, $renewalAmount, $accumulatedAmount, $remark
            ]);

            $recordId = $pdo->lastInsertId();

            // 获取新创建的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $record = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录添加成功',
                'data' => $record
            ]);
            break;

        case 'toggle_record':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 切换完成状态
            $newStatus = $record['is_completed'] ? 0 : 1;

            // 获取记录的最后操作时间（如果没有则使用创建时间）
            $lastOperationTime = $record['updated_at'] ?? $record['created_at'];
            $lastOperationMonth = date('Y-m', strtotime($lastOperationTime));
            $currentMonth = date('Y-m');

            // 计算新的累计金额
            $newAccumulatedAmount = floatval($record['accumulated_amount']);

            // 续期判断函数
            function isRenewalMonth($record) {
                $recordDate = $record['date'];
                $renewalTime = $record['renewal_time'];
                $currentDate = date('Y-m-d');

                // 如果没有续期时间，则不是续期月份
                if (empty($renewalTime) || $renewalTime === '永久') {
                    return false;
                }

                // 解析续期时间
                $monthsToAdd = 0;
                switch ($renewalTime) {
                    case '二个月':
                        $monthsToAdd = 2;
                        break;
                    case '三个月':
                        $monthsToAdd = 3;
                        break;
                    case '六个月':
                        $monthsToAdd = 6;
                        break;
                    default:
                        return false;
                }

                // 计算从记录日期开始的所有续期月份
                $startDate = new DateTime($recordDate);
                $currentDateTime = new DateTime($currentDate);

                // 循环检查是否为续期月份
                $checkDate = clone $startDate;
                while ($checkDate <= $currentDateTime) {
                    $checkDate->add(new DateInterval("P{$monthsToAdd}M"));

                    // 检查当前月份是否为续期月份
                    if ($checkDate->format('Y-m') === $currentDateTime->format('Y-m')) {
                        return true;
                    }

                    // 防止无限循环
                    if ($checkDate->format('Y') > $currentDateTime->format('Y') + 10) {
                        break;
                    }
                }

                return false;
            }

            if ($newStatus == 1) {
                // 从未完成 → 完成：累计金额增加
                $isRenewalMonth = isRenewalMonth($record);
                $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount += $addAmount;
            } else {
                // 从完成 → 未完成：只有当月操作才能减少累计金额
                if ($lastOperationMonth == $currentMonth) {
                    $isRenewalMonth = isRenewalMonth($record);
                    $subtractAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                    $newAccumulatedAmount = max(0, $newAccumulatedAmount - $subtractAmount);
                } else {
                    // 过月锁定，不能减少累计金额
                    http_response_code(400);
                    echo json_encode(['error' => '该记录已过月锁定，无法减少累计金额']);
                    exit;
                }
            }

            // 更新记录，同时更新操作时间
            $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$newStatus, $newAccumulatedAmount, $recordId]);

            // 获取更新后的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $updatedRecord = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '状态更新成功',
                'data' => $updatedRecord
            ]);
            break;

        case 'update_record':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';

            if (empty($recordId) || empty($date) || empty($name) || empty($amount)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 检查是否有累计金额参数
            $accumulatedAmount = $_POST['accumulated_amount'] ?? null;

            if ($accumulatedAmount !== null) {
                // 更新记录包含累计金额
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, accumulated_amount = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark, $accumulatedAmount, $recordId
                ]);
            } else {
                // 更新记录不包含累计金额
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark, $recordId
                ]);
            }

            // 获取更新后的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $updatedRecord = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录更新成功',
                'data' => $updatedRecord
            ]);
            break;

        case 'delete_record':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 删除记录
            $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
            $stmt->execute([$recordId]);

            echo json_encode([
                'success' => true,
                'message' => '记录删除成功'
            ]);
            break;

        case 'add_book':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            if (empty($name)) {
                http_response_code(400);
                echo json_encode(['error' => '账本名称不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 创建账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $name, $description]);

            $bookId = $pdo->lastInsertId();

            // 获取新创建的账本
            $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);
            $book = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '账本创建成功',
                'data' => $book
            ]);
            break;

        case 'delete_book':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本所有权
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '账本不存在或无权限']);
                exit;
            }

            // 检查是否为用户唯一账本
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM account_books WHERE user_id = ?");
            $stmt->execute([$userId]);
            $count = $stmt->fetch()['count'];

            if ($count <= 1) {
                http_response_code(400);
                echo json_encode(['error' => '不能删除唯一的账本']);
                exit;
            }

            // 删除账本（会级联删除相关记录）
            $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);

            echo json_encode([
                'success' => true,
                'message' => '账本删除成功'
            ]);
            break;

        case 'change_password':
            // 验证token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword)) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码和新密码不能为空']);
                exit;
            }

            if (strlen($newPassword) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '新密码至少需要6位']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 获取用户信息
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                http_response_code(404);
                echo json_encode(['error' => '用户不存在']);
                exit;
            }

            // 验证当前密码
            if (!password_verify($currentPassword, $user['password'])) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码错误']);
                exit;
            }

            // 更新密码
            $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$hashedNewPassword, $userId]);

            echo json_encode([
                'success' => true,
                'message' => '密码修改成功'
            ]);
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => '未知操作']);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => '服务器错误',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
