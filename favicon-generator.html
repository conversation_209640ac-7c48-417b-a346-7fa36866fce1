<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
</head>
<body>
    <h2>生成 Favicon</h2>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc; image-rendering: pixelated; width: 128px; height: 128px;"></canvas>
    <br><br>
    <button onclick="generateFavicon()">生成并下载 favicon.ico</button>
    
    <script>
        function generateFavicon() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 32, 32);
            
            // 设置背景色（渐变）
            const gradient = ctx.createLinearGradient(0, 0, 32, 32);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 32, 32);
            
            // 绘制图表图标
            ctx.fillStyle = '#ffffff';
            
            // 绘制柱状图
            ctx.fillRect(6, 20, 4, 8);   // 第一个柱子
            ctx.fillRect(12, 16, 4, 12);  // 第二个柱子
            ctx.fillRect(18, 12, 4, 16);  // 第三个柱子
            ctx.fillRect(24, 8, 4, 20);   // 第四个柱子
            
            // 绘制底线
            ctx.fillRect(4, 28, 24, 2);
            
            // 转换为 ICO 格式并下载
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'favicon.ico';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/x-icon');
        }
        
        // 页面加载时自动绘制预览
        window.onload = function() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置背景色（渐变）
            const gradient = ctx.createLinearGradient(0, 0, 32, 32);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 32, 32);
            
            // 绘制图表图标
            ctx.fillStyle = '#ffffff';
            
            // 绘制柱状图
            ctx.fillRect(6, 20, 4, 8);   // 第一个柱子
            ctx.fillRect(12, 16, 4, 12);  // 第二个柱子
            ctx.fillRect(18, 12, 4, 16);  // 第三个柱子
            ctx.fillRect(24, 8, 4, 20);   // 第四个柱子
            
            // 绘制底线
            ctx.fillRect(4, 28, 24, 2);
        };
    </script>
</body>
</html>
