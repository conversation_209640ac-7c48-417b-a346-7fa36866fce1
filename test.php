<?php
// 系统测试页面

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>记账系统测试</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; background: #f9f9f9; }";
echo ".success { border-left-color: #52c41a; background: #f6ffed; }";
echo ".error { border-left-color: #ff4d4f; background: #fff2f0; }";
echo ".warning { border-left-color: #faad14; background: #fffbe6; }";
echo "h1 { color: #333; text-align: center; }";
echo "h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>记账管理系统 - 环境测试</h1>";

// PHP 版本检查
echo "<h2>PHP 环境检查</h2>";
$phpVersion = phpversion();
echo "<div class='test-item " . (version_compare($phpVersion, '7.4.0', '>=') ? 'success' : 'warning') . "'>";
echo "<strong>PHP 版本:</strong> $phpVersion " . (version_compare($phpVersion, '7.4.0', '>=') ? '✓' : '⚠ (建议 7.4+)');
echo "</div>";

// 扩展检查
$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    echo "<div class='test-item " . (extension_loaded($ext) ? 'success' : 'error') . "'>";
    echo "<strong>$ext 扩展:</strong> " . (extension_loaded($ext) ? '已安装 ✓' : '未安装 ✗');
    echo "</div>";
}

// 数据库连接测试
echo "<h2>数据库连接测试</h2>";
try {
    $dsn = "mysql:host=127.0.0.1;dbname=shuju;charset=utf8mb4";
    $pdo = new PDO($dsn, 'shuju', 'Abc112211', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='test-item success'>";
    echo "<strong>数据库连接:</strong> 成功 ✓";
    echo "</div>";
    
    // 检查表是否存在
    $tables = ['users', 'account_books', 'records'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            echo "<div class='test-item " . ($exists ? 'success' : 'error') . "'>";
            echo "<strong>表 $table:</strong> " . ($exists ? '存在 ✓' : '不存在 ✗');
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='test-item error'>";
            echo "<strong>表 $table:</strong> 检查失败 - " . $e->getMessage();
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>";
    echo "<strong>数据库连接:</strong> 失败 ✗<br>";
    echo "错误信息: " . $e->getMessage();
    echo "</div>";
}

// 文件权限检查
echo "<h2>文件权限检查</h2>";
$paths = [
    'backend/api/config.php' => '配置文件',
    'backend/api/auth.php' => '认证接口',
    'backend/api/account_books.php' => '账本接口',
    'backend/api/records.php' => '记录接口',
    '.htaccess' => '重写规则'
];

foreach ($paths as $path => $name) {
    $exists = file_exists($path);
    $readable = $exists && is_readable($path);
    
    echo "<div class='test-item " . ($readable ? 'success' : 'error') . "'>";
    echo "<strong>$name ($path):</strong> ";
    if ($readable) {
        echo "可访问 ✓";
    } elseif ($exists) {
        echo "存在但不可读 ⚠";
    } else {
        echo "不存在 ✗";
    }
    echo "</div>";
}

// 前端文件检查
echo "<h2>前端文件检查</h2>";
$frontendPath = 'frontend/dist/index.html';
if (file_exists($frontendPath)) {
    echo "<div class='test-item success'>";
    echo "<strong>前端构建文件:</strong> 存在 ✓";
    echo "</div>";
} else {
    echo "<div class='test-item warning'>";
    echo "<strong>前端构建文件:</strong> 不存在 ⚠<br>";
    echo "请在 frontend 目录执行 npm run build";
    echo "</div>";
}

// API 测试
echo "<h2>API 接口测试</h2>";
echo "<div class='test-item'>";
echo "<strong>测试说明:</strong> 请手动测试以下接口<br>";
echo "• 注册: POST /api/auth/register<br>";
echo "• 登录: POST /api/auth/login<br>";
echo "• 账本列表: GET /api/account-books (需要认证)<br>";
echo "</div>";

// 系统信息
echo "<h2>系统信息</h2>";
echo "<div class='test-item'>";
echo "<strong>服务器软件:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<strong>文档根目录:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>当前时间:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "<strong>时区:</strong> " . date_default_timezone_get() . "<br>";
echo "</div>";

echo "<h2>下一步操作</h2>";
echo "<div class='test-item'>";
echo "1. 如果所有检查都通过，可以开始使用系统<br>";
echo "2. 访问首页开始注册和使用<br>";
echo "3. 如有问题，请查看 README.md 文档<br>";
echo "4. 测试完成后可以删除此文件 (test.php)";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
