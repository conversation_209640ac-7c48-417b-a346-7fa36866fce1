# 记账管理系统

一个功能完整的多用户记账网页应用，支持移动端和电脑端，具有自动日期更新、任务完成标记和月度重置功能。

## 功能特性

### 核心功能
- ✅ 多用户支持，每个用户独立数据
- ✅ 多账本管理，每个账本独立数据
- ✅ 完整的记账记录管理（增删改查）
- ✅ 任务完成标记功能（类似待办事项）
- ✅ 月度自动重置功能
- ✅ 累计金额计算和锁定机制
- ✅ 移动端优先的响应式设计

### 记账格式
支持以下字段的记账记录：
- 日期
- 姓名
- 金额
- 每月金额
- 续期时间（二个月/三个月/六个月/永久）
- 续期金额
- 备注
- 累计金额（自动计算）

### 业务规则
1. **续期时间延续机制**：选择续期时间后，到期时会自动顺延相应时间
2. **累计金额规则**：
   - 标记完成 → 累计金额增加
   - 当月取消完成 → 累计金额减少
   - 过月后锁定 → 累计金额不可通过取消完成减少

## 技术栈

### 后端
- PHP 7.4+
- MySQL 5.7+
- 原生 PHP（无框架依赖）
- JWT 认证

### 前端
- Vue.js 3
- Pinia 状态管理
- Ant Design Vue 4
- Vite 构建工具
- WindiCSS 样式框架

### 部署环境
- 宝塔面板 LNMP 环境
- Apache/Nginx
- 支持完全本地化部署

## 安装部署

### 1. 数据库初始化

在 MySQL 中执行以下 SQL 文件：

```bash
mysql -u shuju -p shuju < backend/database/init.sql
```

或者在宝塔面板的 phpMyAdmin 中导入 `backend/database/init.sql` 文件。

### 2. 后端配置

后端配置文件位于 `backend/api/config.php`，已预配置数据库连接信息：

```php
define('DB_HOST', '127.0.0.1');
define('DB_NAME', 'shuju');
define('DB_USER', 'shuju');
define('DB_PASS', 'Abc112211');
```

### 3. 前端构建

在 `frontend` 目录下执行：

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，`frontend/dist` 目录包含所有前端静态文件。

### 4. 服务器配置

确保 `.htaccess` 文件正确配置了路由规则，支持：
- API 请求路由到后端
- 前端 SPA 路由支持
- 静态资源缓存
- Gzip 压缩

### 5. 定时任务（可选）

为了实现每月1号自动重置，可以在宝塔面板中添加定时任务：

```bash
# 每月1号凌晨执行月度重置
0 0 1 * * php /path/to/your/project/backend/cron/monthly_reset.php
```

## API 接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录

### 账本管理
- `GET /api/account-books` - 获取账本列表
- `POST /api/account-books` - 创建账本
- `PUT /api/account-books/{id}` - 更新账本
- `DELETE /api/account-books/{id}` - 删除账本

### 记录管理
- `GET /api/records/{bookId}` - 获取记录列表
- `POST /api/records/{bookId}` - 创建记录
- `PUT /api/records/{bookId}/{recordId}` - 更新记录
- `DELETE /api/records/{bookId}/{recordId}` - 删除记录
- `POST /api/records/{bookId}/{recordId}/toggle` - 切换完成状态
- `POST /api/records/reset-monthly` - 月度重置

## 使用说明

### 首次使用
1. 访问网站，点击"立即注册"创建账户
2. 登录后系统会自动创建默认账本
3. 点击账本进入记录管理页面
4. 点击右下角"+"按钮添加记录

### 记录管理
1. **添加记录**：填写所有必填字段，选择合适的续期时间
2. **完成标记**：点击记录前的复选框标记完成状态
3. **编辑记录**：点击记录右上角的编辑按钮
4. **删除记录**：点击记录右上角的删除按钮

### 月度管理
- 系统支持手动月度重置（右上角菜单）
- 可配置定时任务实现自动重置
- 重置后上月记录被锁定，当月记录取消完成状态

## 移动端适配

应用采用移动端优先设计：
- 响应式布局适配各种屏幕尺寸
- 触摸友好的交互设计
- 优化的表单输入体验
- 流畅的动画效果

## 安全特性

- JWT Token 认证
- 密码哈希存储
- SQL 注入防护
- XSS 攻击防护
- CSRF 保护

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 新增功能

### 数据统计分析
- 总览统计：账本数、记录数、完成率、累计金额
- 月度统计：最近12个月的趋势分析
- 续期时间分布：各续期选项的使用情况
- 账本活跃度：各账本的使用统计

### 数据导出功能
- CSV格式导出：支持Excel打开的中文CSV
- JSON格式导出：完整的结构化数据
- 单账本导出：导出指定账本的数据

### 系统管理工具
- 数据库备份：`backend/utils/backup.php`
- 系统监控：`backend/utils/monitor.php`
- 最终检查：`final_check.php`

## API 接口扩展

### 统计接口
- `GET /api/statistics/overview` - 总览统计
- `GET /api/statistics/monthly` - 月度统计
- `GET /api/statistics/trends` - 趋势分析
- `GET /api/statistics/book/{id}` - 单账本统计

### 导出接口
- `GET /api/export/csv` - 导出CSV格式
- `GET /api/export/json` - 导出JSON格式
- `GET /api/export/book/{id}/csv` - 导出单账本CSV

## 命令行工具

### 数据库备份
```bash
# 创建备份
php backend/utils/backup.php create

# 列出备份
php backend/utils/backup.php list

# 清理旧备份
php backend/utils/backup.php clean 30

# 恢复备份
php backend/utils/backup.php restore backup_2024-01-01_12-00-00.sql
```

### 系统监控
```bash
# 检查系统状态
php backend/utils/monitor.php
```

### 月度重置
```bash
# 手动执行月度重置
php backend/cron/monthly_reset.php
```

## 开发模式

如需进行开发，在 `frontend` 目录下执行：

```bash
npm run dev
```

这将启动开发服务器，支持热重载。

## 部署检查

使用以下工具进行部署检查：

1. **安装向导**: 访问 `install.php`
2. **系统测试**: 访问 `test.php`
3. **最终检查**: 访问 `final_check.php`

## 生产环境优化

1. **安全配置**
   - 修改 `JWT_SECRET` 为随机字符串
   - 关闭 PHP 错误显示
   - 配置 HTTPS
   - 删除测试文件

2. **性能优化**
   - 启用 OPcache
   - 配置 Redis 缓存
   - 优化数据库索引
   - 启用 Gzip 压缩

3. **监控配置**
   - 配置日志轮转
   - 设置监控告警
   - 定期备份数据

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-01-01)
- 完整的多用户记账系统
- 移动端优先设计
- 数据统计分析功能
- 数据导出功能
- 系统管理工具

## 技术支持

如有问题，请检查：
1. 数据库连接配置是否正确
2. PHP 版本是否满足要求（7.4+）
3. 文件权限是否正确设置
4. .htaccess 重写规则是否生效
5. 前端是否已正确构建

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如需技术支持，请通过以下方式联系：
- 查看项目文档
- 运行系统检查工具
- 检查错误日志
