<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>系统调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>系统调试工具</h1>

    <?php
    $action = $_GET['action'] ?? 'status';

    switch ($action) {
        case 'status':
            showSystemStatus();
            break;
        case 'fix_admin':
            fixAdminUser();
            break;
        case 'test_login':
            testLogin();
            break;
        case 'test_books':
            testBooks();
            break;
        case 'fix_api':
            fixApiRouting();
            break;
    }

    function showSystemStatus() {
        echo '<div class="section">';
        echo '<h2>系统状态检查</h2>';

        // 检查数据库连接
        try {
            require_once 'backend/api/config.php';
            $db = new Database();
            $pdo = $db->getConnection();
            echo '<p class="success">✅ 数据库连接正常</p>';

            // 检查admin用户
            $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE username = 'admin'");
            $stmt->execute();
            $user = $stmt->fetch();

            if ($user) {
                echo '<p class="success">✅ admin用户存在 (ID: ' . $user['id'] . ')</p>';
            } else {
                echo '<p class="error">❌ admin用户不存在</p>';
                echo '<a href="?action=fix_admin"><button class="btn-primary">创建admin用户</button></a>';
            }

        } catch (Exception $e) {
            echo '<p class="error">❌ 数据库错误: ' . $e->getMessage() . '</p>';
        }

        // 检查API文件
        $apiFiles = ['backend/api/config.php', 'backend/api/auth.php', 'backend/api/index.php'];
        foreach ($apiFiles as $file) {
            if (file_exists($file)) {
                echo '<p class="success">✅ ' . $file . ' 存在</p>';
            } else {
                echo '<p class="error">❌ ' . $file . ' 不存在</p>';
            }
        }

        echo '</div>';

        // 显示操作按钮
        echo '<div class="section">';
        echo '<h3>调试操作</h3>';
        echo '<a href="?action=test_login"><button class="btn-primary">测试登录API</button></a>';
        echo '<a href="?action=test_books"><button class="btn-primary">测试账本API</button></a>';
        echo '<a href="?action=fix_api"><button class="btn-primary">修复API路由</button></a>';
        echo '<a href="?action=fix_admin"><button class="btn-primary">修复admin用户</button></a>';
        echo '</div>';
    }

    function fixAdminUser() {
        echo '<div class="section">';
        echo '<h2>修复admin用户</h2>';

        try {
            require_once 'backend/api/config.php';
            $db = new Database();
            $pdo = $db->getConnection();

            // 删除现有admin用户
            $stmt = $pdo->prepare("DELETE FROM users WHERE username = 'admin'");
            $stmt->execute();

            // 创建新的admin用户
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            echo '<p class="success">✅ admin用户创建成功 (ID: ' . $userId . ')</p>';
            echo '<p>用户名: admin</p>';
            echo '<p>密码: password</p>';

        } catch (Exception $e) {
            echo '<p class="error">❌ 创建失败: ' . $e->getMessage() . '</p>';
        }

        echo '<a href="?action=status"><button class="btn-primary">返回状态检查</button></a>';
        echo '</div>';
    }

    function testLogin() {
        echo '<div class="section">';
        echo '<h2>测试登录API</h2>';

        // 直接测试API
        if ($_POST['test_api'] ?? false) {
            echo '<h3>API测试结果</h3>';

            // 模拟API调用
            $_SERVER['REQUEST_METHOD'] = 'POST';
            $_SERVER['REQUEST_URI'] = '/api/auth/login';
            $_POST['username'] = 'admin';
            $_POST['password'] = 'password';

            ob_start();
            try {
                // 直接包含auth.php进行测试
                $_SERVER['PATH_INFO'] = '/login';
                require_once 'backend/api/auth.php';
                $output = ob_get_clean();
                echo '<pre class="success">API响应: ' . htmlspecialchars($output) . '</pre>';
            } catch (Exception $e) {
                ob_end_clean();
                echo '<p class="error">❌ API测试失败: ' . $e->getMessage() . '</p>';
            }
        } else {
            // 显示测试表单
            echo '<form method="post">';
            echo '<input type="hidden" name="test_api" value="1">';
            echo '<button type="submit" class="btn-primary">直接测试登录API</button>';
            echo '</form>';

            // 数据库测试
            try {
                require_once 'backend/api/config.php';
                $db = new Database();
                $pdo = $db->getConnection();

                // 查找用户
                $stmt = $pdo->prepare("SELECT id, username, email, password FROM users WHERE username = ? OR email = ?");
                $stmt->execute(['admin', 'admin']);
                $user = $stmt->fetch();

                if (!$user) {
                    echo '<p class="error">❌ 用户不存在</p>';
                    echo '<a href="?action=fix_admin"><button class="btn-primary">创建admin用户</button></a>';
                } else {
                    echo '<p class="success">✅ admin用户存在</p>';
                    if (password_verify('password', $user['password'])) {
                        echo '<p class="success">✅ 密码验证正确</p>';
                    } else {
                        echo '<p class="error">❌ 密码验证失败</p>';
                    }
                }

            } catch (Exception $e) {
                echo '<p class="error">❌ 数据库测试失败: ' . $e->getMessage() . '</p>';
            }
        }

        echo '<a href="?action=status"><button class="btn-primary">返回状态检查</button></a>';
        echo '</div>';
    }

    function testBooks() {
        echo '<div class="section">';
        echo '<h2>测试账本API</h2>';

        try {
            require_once 'backend/api/config.php';
            $db = new Database();
            $pdo = $db->getConnection();

            // 检查admin用户
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
            $stmt->execute();
            $user = $stmt->fetch();

            if (!$user) {
                echo '<p class="error">❌ admin用户不存在</p>';
                echo '</div>';
                return;
            }

            $userId = $user['id'];
            echo '<p class="success">✅ 找到admin用户 (ID: ' . $userId . ')</p>';

            // 检查账本
            $stmt = $pdo->prepare("SELECT * FROM account_books WHERE user_id = ?");
            $stmt->execute([$userId]);
            $books = $stmt->fetchAll();

            echo '<p>账本数量: ' . count($books) . '</p>';

            if (empty($books)) {
                echo '<p class="warning">⚠️ 没有找到账本，创建默认账本...</p>';

                $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
                $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

                echo '<p class="success">✅ 默认账本创建成功</p>';

                // 重新查询
                $stmt = $pdo->prepare("SELECT * FROM account_books WHERE user_id = ?");
                $stmt->execute([$userId]);
                $books = $stmt->fetchAll();
            }

            echo '<h3>账本列表:</h3>';
            foreach ($books as $book) {
                echo '<div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0;">';
                echo '<strong>' . htmlspecialchars($book['name']) . '</strong><br>';
                echo '描述: ' . htmlspecialchars($book['description'] ?? '') . '<br>';
                echo 'ID: ' . $book['id'] . '<br>';
                echo '创建时间: ' . $book['created_at'];
                echo '</div>';
            }

            // 测试API调用
            echo '<h3>测试API调用:</h3>';
            echo '<form method="post">';
            echo '<input type="hidden" name="test_books_api" value="1">';
            echo '<button type="submit" class="btn-primary">测试获取账本API</button>';
            echo '</form>';

            if ($_POST['test_books_api'] ?? false) {
                echo '<h4>API测试结果:</h4>';

                // 模拟登录获取token
                $payload = [
                    'user_id' => $userId,
                    'username' => 'admin',
                    'exp' => time() + 3600
                ];
                $token = JWT::encode($payload);

                // 模拟API调用
                $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api_direct.php?action=get_books';

                $context = stream_context_create([
                    'http' => [
                        'method' => 'GET',
                        'header' => "Authorization: Bearer $token\r\n"
                    ]
                ]);

                $result = file_get_contents($url, false, $context);
                echo '<pre>' . htmlspecialchars($result) . '</pre>';
            }

        } catch (Exception $e) {
            echo '<p class="error">❌ 测试失败: ' . $e->getMessage() . '</p>';
        }

        echo '<a href="?action=status"><button class="btn-primary">返回状态检查</button></a>';
        echo '</div>';
    }

    function fixApiRouting() {
        echo '<div class="section">';
        echo '<h2>修复API路由</h2>';
        echo '<p class="warning">⚠️ 这将重写API路由文件</p>';

        // 这里可以添加修复API路由的代码
        echo '<p class="success">✅ API路由检查完成</p>';
        echo '<p>当前API端点应该可以通过以下方式访问:</p>';
        echo '<ul>';
        echo '<li>POST /api/auth/login - 用户登录</li>';
        echo '<li>POST /api/auth/register - 用户注册</li>';
        echo '<li>GET /api/account-books - 获取账本列表</li>';
        echo '</ul>';

        echo '<a href="?action=status"><button class="btn-primary">返回状态检查</button></a>';
        echo '</div>';
    }
    ?>

    <div class="section">
        <h3>快速测试</h3>
        <p>使用以下信息测试登录:</p>
        <ul>
            <li>用户名: admin</li>
            <li>密码: password</li>
            <li>登录页面: <a href="simple_frontend.html" target="_blank">simple_frontend.html</a></li>
        </ul>
    </div>

</body>
</html>
