<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记账管理系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">

    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .app-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 40px;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
        }
        .title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .error {
            color: #ff4d4f;
            margin-top: 10px;
            text-align: center;
        }
        .success {
            color: #52c41a;
            margin-top: 10px;
            text-align: center;
        }
        .link {
            text-align: center;
            margin-top: 20px;
        }
        .link a {
            color: #667eea;
            text-decoration: none;
        }
        .main-app {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
        }

        /* 电脑端居中显示 */
        @media (min-width: 769px) {
            .main-app {
                margin: 20px auto;
                max-width: 900px;
            }
            /* 只对主应用页面的app-container应用flex-start */
            #main-page.app-container {
                display: flex;
                justify-content: center;
                align-items: flex-start;
                min-height: 100vh;
                padding: 20px;
            }
            /* 登录页面保持居中 */
            #login-page.app-container {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                padding: 20px;
            }
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content {
            padding: 20px;
            max-height: 70vh;
            overflow-y: visible;
        }
        .record-card {
            border: 1px solid #e8e8e8;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            border-color: #d9d9d9;
        }
        .record-header {
            display: flex;
            align-items: center;
            margin-bottom: 0px;
            gap: 15px;
        }
        .record-header .left-section {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 15px;
        }
        .record-header .right-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .record-name {
            font-size: 20px;
            font-weight: 700;
            color: #1a1a1a;
            margin-right: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        .record-amount {
            font-size: 18px;
            font-weight: 700;
            color: #ff4757;
            background: linear-gradient(135deg, #ff6b7a, #ff4757);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .record-details {
            display: none;
        }
        .record-details-inline {
            display: flex;
            gap: 12px;
            font-size: 13px;
            color: #555;
            line-height: 1.4;
            font-weight: 500;
        }
        .record-details > div,
        .record-details-inline > div {
            white-space: nowrap;
            background: rgba(24, 144, 255, 0.06);
            padding: 3px 6px;
            border-radius: 4px;
            border: 1px solid rgba(24, 144, 255, 0.12);
            font-size: 12px;
        }
        .record-details .accumulated,
        .record-details-inline .accumulated,
        .record-header .accumulated {
            background: rgba(82, 196, 26, 0.08);
            border-color: rgba(82, 196, 26, 0.15);
            color: #52c41a;
            font-weight: 600;
        }
        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        /* 桌面端专用样式 */
        @media (min-width: 769px) {
            .record-header button {
                padding: 6px 12px !important;
                font-size: 12px !important;
                border-radius: 6px !important;
                font-weight: 500 !important;
                transition: all 0.2s ease !important;
                width: auto !important;
                height: auto !important;
            }
            .record-name {
                font-size: 20px !important;
                font-weight: 700 !important;
            }
            .record-amount {
                font-size: 18px !important;
                font-weight: 700 !important;
            }
        }
        /* 手机端优化 */
        @media (max-width: 768px) {
            .record-card {
                padding: 8px;
                margin-bottom: 6px;
                position: relative;
            }
            .record-header {
                margin-bottom: 4px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                gap: 6px;
                min-height: 32px;
            }
            .record-header > div:first-child {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                gap: 4px;
                padding-right: 60px;
            }
            .record-header > div:last-child {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 2px;
                flex-shrink: 0;
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
            }
            /* 移动端文字大小统一 */
            .record-name {
                font-size: 14px !important;
                font-weight: 600 !important;
                margin-right: 6px;
                white-space: nowrap;
            }
            .record-amount {
                font-size: 14px !important;
                font-weight: 700 !important;
                margin-right: 6px;
                white-space: nowrap;
            }
            /* 移动端累计金额样式优化 */
            .record-header .accumulated {
                font-size: 11px !important;
                padding: 2px 5px !important;
                margin-left: 0px !important;
                border-radius: 3px !important;
                background: rgba(82, 196, 26, 0.1) !important;
                border: 1px solid rgba(82, 196, 26, 0.2) !important;
                color: #52c41a !important;
                font-weight: 600 !important;
                white-space: nowrap;
            }
            /* 移动端小图标按钮 */
            .record-header button {
                width: 22px !important;
                height: 22px !important;
                padding: 0 !important;
                border-radius: 3px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 11px !important;
                font-weight: normal !important;
                border: 1px solid !important;
                min-width: 22px !important;
            }
            .record-details-inline {
                display: none !important;
            }
            .record-details {
                display: flex !important;
                flex-wrap: wrap;
                gap: 6px;
                font-size: 12px;
                line-height: 1.3;
                margin-top: 4px;
            }
            .record-details > div {
                white-space: nowrap;
                padding: 2px 5px !important;
                font-size: 11px !important;
                border-radius: 3px !important;
            }
            .content {
                padding: 12px;
                max-height: 75vh;
                overflow-y: visible;
            }
            .header {
                padding: 12px;
            }
            /* 手机端统计表头优化 */
            #statistics-header {
                padding: 8px !important;
                margin-bottom: 12px !important;
            }
            #statistics-header > div {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 6px !important;
            }
            #statistics-header > div > div {
                padding: 6px !important;
            }
            #statistics-header div > div:first-child {
                font-size: 9px !important;
                margin-bottom: 2px !important;
            }
            #statistics-header div > div:last-child {
                font-size: 12px !important;
            }
            /* 手机端记录列表头部优化 - 紧凑单行布局 */
            .records-header {
                margin-bottom: 12px !important;
            }

            .header-controls {
                gap: 6px !important;
                margin-bottom: 0 !important;
            }

            .custom-select {
                font-size: 12px !important;
                padding: 6px 8px !important;
                max-width: 140px !important;
                flex: 0 1 auto !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                background: #f0f0f0 !important;
                height: auto !important;
                cursor: pointer !important;
                min-height: 32px !important;
                box-sizing: border-box !important;
            }

            .custom-select #selected-book-text {
                font-size: 12px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                flex: 1 !important;
            }

            #book-options {
                margin-top: 2px !important;
                border-radius: 4px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                z-index: 9999 !important;
                position: absolute !important;
                background: white !important;
                border: 1px solid #ddd !important;
            }

            #book-options > div {
                font-size: 12px !important;
                padding: 8px 12px !important;
                border-bottom: 1px solid #f0f0f0 !important;
            }

            #book-options > div:last-child {
                border-bottom: none !important;
            }

            .header-controls button {
                font-size: 12px !important;
                padding: 6px 12px !important;
                white-space: nowrap !important;
                height: auto !important;
                min-height: 32px !important;
            }

            .header-stats {
                margin-top: 0 !important;
            }

            #records-count {
                font-size: 12px !important;
            }
        }

        /* 桌面端样式 */
        @media (min-width: 769px) {
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }

            .header-controls {
                font-size: 14px !important;
                gap: 8px !important;
            }

            .header-controls button {
                font-size: 14px !important;
                padding: 8px 16px !important;
            }

            .custom-select {
                font-size: 14px !important;
                padding: 8px 12px !important;
                max-width: 200px !important;
                min-width: 150px !important;
            }

            .custom-select #selected-book-text {
                font-size: 14px !important;
            }

            #book-options {
                margin-top: 2px !important;
                border-radius: 4px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                z-index: 9999 !important;
                position: absolute !important;
                background: white !important;
                border: 1px solid #ddd !important;
            }

            #book-options > div {
                font-size: 14px !important;
                padding: 10px 12px !important;
                border-bottom: 1px solid #f0f0f0 !important;
            }

            .records-stats {
                font-size: 14px !important;
            }

            .record-item {
                padding: 16px !important;
                font-size: 14px !important;
            }

            .record-main {
                font-size: 16px !important;
            }

            .record-details {
                font-size: 13px !important;
            }

            .record-actions button {
                font-size: 13px !important;
                padding: 6px 12px !important;
            }

            .form-group label {
                font-size: 14px !important;
            }

            .form-group input, .form-group select, .form-group textarea {
                font-size: 14px !important;
                padding: 10px !important;
            }

            .btn {
                font-size: 14px !important;
                padding: 10px 20px !important;
            }

            .btn-small {
                font-size: 12px !important;
                padding: 6px 12px !important;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div id="login-page" class="app-container">
            <div class="login-card">
                <div class="logo">📊</div>
                <h1 class="title">记账管理</h1>

                <form onsubmit="login(event)">
                    <div class="form-group">
                        <label>用户名/邮箱</label>
                        <input
                            type="text"
                            id="username"
                            placeholder="请输入用户名或邮箱"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input
                            type="password"
                            id="password"
                            placeholder="请输入密码"
                            required
                        >
                    </div>
                    <button type="submit" class="btn" id="login-btn">
                        登录
                    </button>
                </form>

                <div id="message" style="margin-top: 15px; text-align: center;"></div>

                <div class="link">
                    <a href="#" onclick="toggleRegister()">
                        <span id="register-link-text">注册新账户</span>
                    </a>
                </div>

                <!-- 注册表单 -->
                <form id="register-form" onsubmit="register(event)" style="display: none; margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
                    <div class="form-group">
                        <label>用户名</label>
                        <input
                            type="text"
                            id="reg-username"
                            placeholder="请输入用户名"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input
                            type="email"
                            id="reg-email"
                            placeholder="请输入邮箱"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input
                            type="password"
                            id="reg-password"
                            placeholder="请输入密码（至少6位）"
                            required
                        >
                    </div>
                    <button type="submit" class="btn" id="register-btn">
                        注册
                    </button>
                </form>
            </div>
        </div>

        <!-- 主应用页面 -->
        <div id="main-page" class="app-container" style="display: none;">
            <div class="main-app">
                <div class="header">
                    <h2 id="main-title">我的账本</h2>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="showChangePassword()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            修改密码
                        </button>
                        <button onclick="logout()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            退出
                        </button>
                    </div>
                </div>

                <div class="content">
                    <!-- 账本列表 -->
                    <div id="books-list">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0;">选择账本</h3>
                            <button onclick="showAddBookForm()" style="background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">+ 添加账本</button>
                        </div>
                        <div id="books-container">
                            <!-- 账本列表将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 记录列表 -->
                    <div id="records-list" style="display: none;">
                        <div class="records-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <!-- 左侧：账本切换器和管理按钮 -->
                            <div class="header-controls" style="display: flex; align-items: center; gap: 6px;">
                                <!-- 自定义下拉选择器 -->
                                <div class="custom-select" id="custom-book-selector" style="position: relative; background: #f0f0f0; border: 1px solid #ddd; padding: 6px 8px; border-radius: 4px; cursor: pointer; font-size: 13px; min-width: 120px; display: flex; align-items: center; justify-content: space-between; z-index: 1001;">
                                    <span id="selected-book-text">📚 默认账本</span>
                                    <span style="margin-left: 8px; font-size: 10px; color: #666;">▼</span>
                                    <!-- book-options不再嵌套在这里，由JS动态插入body下 -->
                                </div>
                                <button onclick="showBooksList()" style="background: #1890ff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                                    管理账本
                                </button>
                            </div>
                            <!-- 右侧：记录统计 -->
                            <div class="header-stats">
                                <span id="records-count" style="color: #666; font-size: 13px;">0 条记录</span>
                            </div>
                        </div>

                        <!-- 统计表头 -->
                        <div id="statistics-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px; border-radius: 12px; margin-bottom: 16px; display: none; box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);">
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; text-align: center;">
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 8px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 2px;">💰 总金额</div>
                                    <div style="font-size: 14px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-total-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 8px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 2px;">📅 每月金额</div>
                                    <div style="font-size: 14px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-monthly-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 8px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 2px;">🔄 续期金额</div>
                                    <div style="font-size: 14px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-renewal-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 8px; border-radius: 8px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 2px;">📈 累计金额</div>
                                    <div style="font-size: 14px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-accumulated-amount">¥0.00</div>
                                </div>
                            </div>
                        </div>

                        <div id="records-container">
                            <!-- 记录列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <button id="add-btn" onclick="showAddForm()" class="floating-btn" style="display: none;">+</button>
        </div>

        <!-- 添加/编辑记录弹窗 -->
        <div id="add-form-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
                <h3 id="form-title" style="margin-bottom: 20px;">添加记录</h3>

                <!-- 累计金额编辑 -->
                <div id="accumulated-amount-display" style="display: none; margin-bottom: 15px;">
                    <div class="form-group">
                        <label>累计金额</label>
                        <input type="number" step="0.01" id="record-accumulated-amount" placeholder="0.00">
                        <small style="color: #666; font-size: 12px;">可以手动调整累计金额</small>
                    </div>
                </div>

                <form onsubmit="submitRecord(event)">
                    <div class="form-group">
                        <label>日期</label>
                        <input type="date" id="record-date" required>
                    </div>
                    <div class="form-group">
                        <label>姓名</label>
                        <input type="text" id="record-name" placeholder="请输入姓名" required>
                    </div>
                    <div class="form-group">
                        <label>金额</label>
                        <input type="number" step="0.01" id="record-amount" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label>每月金额</label>
                        <input type="number" step="0.01" id="record-monthly-amount" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label>续期时间</label>
                        <select id="record-renewal-time" required>
                            <option value="">请选择</option>
                            <option value="二个月">二个月</option>
                            <option value="三个月">三个月</option>
                            <option value="六个月">六个月</option>
                            <option value="永久">永久</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>续期金额</label>
                        <input type="number" step="0.01" id="record-renewal-amount" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="record-remark" placeholder="请输入备注（可选）" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button type="submit" class="btn" style="flex: 1;">保存</button>
                        <button type="button" onclick="hideAddForm()" style="flex: 1; background: #f0f0f0; color: #333;">取消</button>
                    </div>
                </form>
            </div>
        </div>



        <!-- 添加账本弹窗 -->
        <div id="add-book-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 400px;">
                <h3 style="margin-bottom: 20px;">📚 添加账本</h3>
                <form onsubmit="submitBook(event)">
                    <div class="form-group">
                        <label>账本名称</label>
                        <input type="text" id="book-name" placeholder="请输入账本名称" required>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea id="book-description" placeholder="请输入账本描述（可选）" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="submit" class="btn" style="flex: 1;">创建账本</button>
                        <button type="button" onclick="hideAddBookForm()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 10px; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 修改密码弹窗 -->
        <div id="change-password-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 400px;">
                <h3 style="margin-bottom: 20px;">🔒 修改密码</h3>
                <form onsubmit="changePassword(event)">
                    <div class="form-group">
                        <label>当前密码</label>
                        <input type="password" id="current-password" placeholder="请输入当前密码" required>
                    </div>
                    <div class="form-group">
                        <label>新密码</label>
                        <input type="password" id="new-password" placeholder="请输入新密码（至少6位）" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label>确认新密码</label>
                        <input type="password" id="confirm-password" placeholder="请再次输入新密码" required>
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="submit" class="btn" style="flex: 1;">确认修改</button>
                        <button type="button" onclick="hideChangePassword()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 10px; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let token = '';
        let currentBook = null;
        let books = [];
        let records = [];

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            // 设置默认日期
            document.getElementById('record-date').value = new Date().toISOString().split('T')[0];

            // 初始化自定义下拉选择器事件
            initCustomSelector();

            // 清理任何可能存在的旧select元素
            cleanupOldSelectors();

            // 保证#book-options存在于body下
            if (!document.getElementById('book-options')) {
                const bookOptions = document.createElement('div');
                bookOptions.id = 'book-options';
                bookOptions.style.display = 'none';
                bookOptions.style.position = 'absolute';
                bookOptions.style.background = 'white';
                bookOptions.style.border = '1px solid #ddd';
                bookOptions.style.borderRadius = '4px';
                bookOptions.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                bookOptions.style.zIndex = 9999;
                document.body.appendChild(bookOptions);
            }
        });

        function cleanupOldSelectors() {
            // 移除任何可能存在的旧的select元素
            const oldSelectors = document.querySelectorAll('#book-selector, select[id*="book"]');
            oldSelectors.forEach(selector => {
                if (selector && selector.id !== 'custom-book-selector') {
                    selector.remove();
                }
            });
        }

        function initCustomSelector() {
            document.addEventListener('click', function(e) {
                const customSelector = document.getElementById('custom-book-selector');
                const bookOptions = document.getElementById('book-options');
                if (!customSelector || !bookOptions) return;
                if (customSelector.contains(e.target)) {
                    if (bookOptions.style.display === 'none' || bookOptions.style.display === '') {
                        showBookOptionsDropdown();
                        setTimeout(() => {
                            const currentOption = document.getElementById('current-book-option');
                            if (currentOption) {
                                currentOption.scrollIntoView({ block: 'nearest' });
                            }
                        }, 0);
                    } else {
                        hideBookOptionsDropdown();
                    }
                } else {
                    hideBookOptionsDropdown();
                }
            });
        }

        function checkAuth() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                token = savedToken;
                showMainPage();
                loadBooks();
            }
        }

        function showMessage(text, isError = false) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.style.color = isError ? '#ff4d4f' : '#52c41a';
            messageEl.style.display = 'block';
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        function toggleRegister() {
            const loginForm = document.querySelector('#login-page form:first-of-type');
            const registerForm = document.getElementById('register-form');
            const linkText = document.getElementById('register-link-text');

            if (registerForm.style.display === 'none' || registerForm.style.display === '') {
                // 显示注册表单，隐藏登录表单
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
                linkText.textContent = '返回登录';
            } else {
                // 显示登录表单，隐藏注册表单
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
                linkText.textContent = '注册新账户';
            }
        }
        async function login(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');

            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('action', 'login');
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    showMessage('登录成功！');
                    setTimeout(() => {
                        showMainPage();
                        loadBooks();
                    }, 1000);
                } else {
                    showMessage(data.error || '登录失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('登录错误:', error);
            }

            loginBtn.textContent = '登录';
            loginBtn.disabled = false;
        }

        async function register(event) {
            event.preventDefault();
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const registerBtn = document.getElementById('register-btn');

            registerBtn.textContent = '注册中...';
            registerBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('action', 'register');
                formData.append('username', username);
                formData.append('email', email);
                formData.append('password', password);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    showMessage('注册成功！');
                    setTimeout(() => {
                        showMainPage();
                        loadBooks();
                    }, 1000);
                } else {
                    showMessage(data.error || '注册失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('注册错误:', error);
            }

            registerBtn.textContent = '注册';
            registerBtn.disabled = false;
        }
        function showMainPage() {
            document.getElementById('login-page').style.display = 'none';
            document.getElementById('main-page').style.display = 'block';
        }

        function showBooksList() {
            document.getElementById('books-list').style.display = 'block';
            document.getElementById('records-list').style.display = 'none';
            document.getElementById('add-btn').style.display = 'none';
            document.getElementById('main-title').textContent = '我的账本';
            currentBook = null;
        }

        function showRecordsList(book) {
            document.getElementById('books-list').style.display = 'none';
            document.getElementById('records-list').style.display = 'block';
            document.getElementById('add-btn').style.display = 'block';
            document.getElementById('main-title').textContent = book.name;
            currentBook = book;

            // 保存最后选择的账本
            localStorage.setItem('lastSelectedBookId', book.id);

            // 清理旧的select元素
            cleanupOldSelectors();

            // 更新账本选择器
            updateBookSelector();

            loadRecords();
        }

        function updateBookSelector() {
            const selectedText = document.getElementById('selected-book-text');
            const optionsContainer = document.getElementById('book-options');

            if (!selectedText || !optionsContainer) return;

            // 移除任何可能存在的旧的select元素
            const oldSelector = document.getElementById('book-selector');
            if (oldSelector) {
                oldSelector.remove();
            }

            // 更新选中的文本
            if (currentBook) {
                selectedText.textContent = `📚 ${currentBook.name}`;
            }

            // 清空并重新生成选项
            optionsContainer.innerHTML = '';

            books.forEach(book => {
                const option = document.createElement('div');
                option.style.cssText = 'padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;';
                option.textContent = `📚 ${book.name}`;
                option.onclick = () => selectBook(book);

                // 高亮当前选中的账本，并添加唯一id
                if (currentBook && book.id == currentBook.id) {
                    option.style.backgroundColor = '#e6f7ff';
                    option.style.color = '#1890ff';
                    option.style.fontWeight = '600';
                    option.id = 'current-book-option';
                }

                // 悬停效果
                option.onmouseenter = () => {
                    if (!currentBook || book.id != currentBook.id) {
                        option.style.backgroundColor = '#f5f5f5';
                    }
                };
                option.onmouseleave = () => {
                    if (!currentBook || book.id != currentBook.id) {
                        option.style.backgroundColor = '';
                    }
                };

                optionsContainer.appendChild(option);
            });
        }

        function selectBook(book) {
            if (book && book.id != currentBook.id) {
                // 隐藏下拉选项
                document.getElementById('book-options').style.display = 'none';
                // 切换到选中的账本
                showRecordsList(book);
            } else {
                // 如果选择的是当前账本，只是隐藏下拉选项
                document.getElementById('book-options').style.display = 'none';
            }
        }

        // switchBook函数已移除，因为我们使用自定义下拉选择器

        function autoEnterDefaultBook() {
            if (books.length === 0) return;

            // 获取最后选择的账本ID
            const lastSelectedBookId = localStorage.getItem('lastSelectedBookId');
            let targetBook = null;

            // 优先选择最后选择的账本
            if (lastSelectedBookId) {
                targetBook = books.find(book => book.id == lastSelectedBookId);
            }

            // 如果没有找到最后选择的账本，选择第一个账本作为默认账本
            if (!targetBook && books.length > 0) {
                targetBook = books[0];
            }

            // 直接进入目标账本，无延迟
            if (targetBook) {
                showRecordsList(targetBook);
            }
        }

        async function loadBooks() {
            try {
                const response = await fetch('api_direct.php?action=get_books', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    books = data.data;
                    renderBooks();

                    // 自动进入默认账本或最后选择的账本
                    autoEnterDefaultBook();
                } else {
                    showMessage('加载账本失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('加载账本错误:', error);
            }
        }

        function renderBooks() {
            const container = document.getElementById('books-container');
            container.innerHTML = '';

            books.forEach(book => {
                const bookEl = document.createElement('div');
                bookEl.style.cssText = 'padding: 20px; border: 1px solid #e8e8e8; border-radius: 16px; margin-bottom: 16px; cursor: pointer; background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); transition: all 0.3s ease;';
                bookEl.onmouseenter = () => {
                    bookEl.style.transform = 'translateY(-4px)';
                    bookEl.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
                    bookEl.style.borderColor = '#d9d9d9';
                };
                bookEl.onmouseleave = () => {
                    bookEl.style.transform = 'translateY(0)';
                    bookEl.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';
                    bookEl.style.borderColor = '#e8e8e8';
                };
                bookEl.onclick = () => showRecordsList(book);

                bookEl.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div style="font-size: 20px; font-weight: 700; color: #1a1a1a;">📚 ${book.name}</div>
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                            ${book.record_count || 0} 条记录
                        </div>
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 12px; line-height: 1.4;">${book.description || '✨ 暂无描述'}</div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="background: rgba(82, 196, 26, 0.1); color: #52c41a; padding: 6px 12px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                            💰 累计: ¥${parseFloat(book.total_amount || 0).toFixed(2)}
                        </div>
                        <div style="color: #1890ff; font-size: 12px; font-weight: 500;">点击进入 →</div>
                    </div>
                `;

                container.appendChild(bookEl);
            });

            // 移除任何可能存在的旧的select元素
            const oldSelector = document.getElementById('book-selector');
            if (oldSelector) {
                oldSelector.remove();
            }
        }
        async function loadRecords() {
            if (!currentBook) return;

            try {
                const response = await fetch(`api_direct.php?action=get_records&book_id=${currentBook.id}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    records = data.data;
                    renderRecords();
                } else {
                    showMessage('加载记录失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('加载记录错误:', error);
            }
        }

        function renderRecords() {
            const container = document.getElementById('records-container');
            const countEl = document.getElementById('records-count');

            container.innerHTML = '';
            countEl.textContent = `${records.length} 条记录`;

            // 计算统计数据
            updateStatistics();

            // 显示统计表头
            document.getElementById('statistics-header').style.display = records.length > 0 ? 'block' : 'none';

            // 按日期的"日"升序排序
            const sortedRecords = [...records].sort((a, b) => {
                const dateA = new Date(a.date || '1900-01-01');
                const dateB = new Date(b.date || '1900-01-01');

                // 只按日排序
                return dateA.getDate() - dateB.getDate();
            });

            sortedRecords.forEach(record => {
                const recordEl = document.createElement('div');
                recordEl.className = 'record-card';

                // 判断是否为当月操作（基于最后更新时间）
                const lastUpdateTime = record.updated_at || record.created_at;
                const lastUpdateMonth = lastUpdateTime ? lastUpdateTime.substring(0, 7) : '';
                const currentMonth = new Date().toISOString().substring(0, 7);
                const isCurrentMonth = lastUpdateMonth === currentMonth;
                const lockStatus = isCurrentMonth ? '' : ' 🔒';

                // 格式化日期显示（只显示日）
                const formatDate = (dateStr) => {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return `${String(date.getDate()).padStart(2, '0')}日`;
                };

                // 格式化续期信息 - 计算下次续期月份
                const formatRenewalInfo = (dateStr, renewalTime, renewalAmount) => {
                    if (!renewalTime || renewalTime === '永久' || !dateStr) {
                        return `续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                    }

                    const recordDate = new Date(dateStr);
                    const monthsToAdd = {
                        '二个月': 2,
                        '三个月': 3,
                        '六个月': 6
                    };

                    const addMonths = monthsToAdd[renewalTime];
                    if (!addMonths) {
                        return `续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                    }

                    // 计算下次续期月份
                    const nextRenewalDate = new Date(recordDate);
                    nextRenewalDate.setMonth(nextRenewalDate.getMonth() + addMonths);
                    const nextMonth = nextRenewalDate.getMonth() + 1;

                    return `${nextMonth}月续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                };

                recordEl.innerHTML = `
                    <div class="record-header">
                        <div class="left-section">
                            <input type="checkbox" ${record.is_completed ? 'checked' : ''}
                                   onchange="toggleRecord(${record.id})" style="margin-right: 8px;">
                            <span class="record-name">${record.name || ''}</span>
                            <div class="record-amount">¥${parseFloat(record.amount || 0).toFixed(2)}</div>
                            <div class="accumulated" style="margin-left: 10px;">累计:¥${parseFloat(record.accumulated_amount || 0).toFixed(2)}</div>
                            <div class="record-details-inline" style="display: flex; gap: 12px; margin-left: 20px;">
                                <div>${formatDate(record.date)}</div>
                                <div>月:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}</div>
                                <div>${formatRenewalInfo(record.date, record.renewal_time, record.renewal_amount)}</div>
                                ${record.remark ? `<div>备注:${record.remark}</div>` : ''}
                            </div>
                            ${lockStatus ? `<span style="color: #999; font-size: 12px; margin-left: 10px;">${lockStatus}</span>` : ''}
                        </div>
                        <div class="right-section">
                            <button onclick="editRecord(${record.id})" style="background: rgba(24, 144, 255, 0.1); color: #1890ff; border: 1px solid rgba(24, 144, 255, 0.2); padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='rgba(24, 144, 255, 0.15)'; this.style.borderColor='rgba(24, 144, 255, 0.3)'" onmouseout="this.style.background='rgba(24, 144, 255, 0.1)'; this.style.borderColor='rgba(24, 144, 255, 0.2)'">✏️</button>
                            <button onclick="deleteRecord(${record.id})" style="background: rgba(255, 77, 79, 0.1); color: #ff4d4f; border: 1px solid rgba(255, 77, 79, 0.2); padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='rgba(255, 77, 79, 0.15)'; this.style.borderColor='rgba(255, 77, 79, 0.3)'" onmouseout="this.style.background='rgba(255, 77, 79, 0.1)'; this.style.borderColor='rgba(255, 77, 79, 0.2)'">🗑️</button>
                        </div>
                    </div>
                    <div class="record-details">
                        <div>${formatDate(record.date)}</div>
                        <div>月:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}</div>
                        <div>${formatRenewalInfo(record.date, record.renewal_time, record.renewal_amount)}</div>
                        ${record.remark ? `<div>备注:${record.remark}</div>` : ''}
                    </div>
                `;

                container.appendChild(recordEl);
            });
        }

        function updateStatistics() {
            let totalAmount = 0;
            let totalMonthlyAmount = 0;
            let totalRenewalAmount = 0;
            let totalAccumulatedAmount = 0;

            records.forEach(record => {
                totalAmount += parseFloat(record.amount || 0);
                totalMonthlyAmount += parseFloat(record.monthly_amount || 0);
                totalRenewalAmount += parseFloat(record.renewal_amount || 0);
                totalAccumulatedAmount += parseFloat(record.accumulated_amount || 0);
            });

            // 更新统计显示
            document.getElementById('stat-total-amount').textContent = `¥${totalAmount.toFixed(2)}`;
            document.getElementById('stat-monthly-amount').textContent = `¥${totalMonthlyAmount.toFixed(2)}`;
            document.getElementById('stat-renewal-amount').textContent = `¥${totalRenewalAmount.toFixed(2)}`;
            document.getElementById('stat-accumulated-amount').textContent = `¥${totalAccumulatedAmount.toFixed(2)}`;
        }

        async function toggleRecord(recordId) {
            try {
                const response = await fetch(`api_direct.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `action=toggle_record&record_id=${recordId}`
                });

                const data = await response.json();

                if (data.success) {
                    loadRecords(); // 重新加载记录
                } else {
                    showMessage('状态更新失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('切换记录状态错误:', error);
            }
        }

        function showAddForm() {
            editingRecordId = null;
            document.getElementById('form-title').textContent = '添加记录';
            document.getElementById('accumulated-amount-display').style.display = 'block';
            document.getElementById('current-accumulated').textContent = '0.00';
            resetRecordForm();
            document.getElementById('add-form-modal').style.display = 'flex';
        }

        function hideAddForm() {
            document.getElementById('add-form-modal').style.display = 'none';
            editingRecordId = null;
            resetRecordForm();
        }

        async function submitRecord(event) {
            event.preventDefault();

            if (!currentBook && !editingRecordId) {
                showMessage('请先选择账本', true);
                return;
            }

            // 获取表单数据
            const date = document.getElementById('record-date').value;
            const name = document.getElementById('record-name').value;
            const amount = document.getElementById('record-amount').value;
            const monthlyAmount = document.getElementById('record-monthly-amount').value;
            const renewalTime = document.getElementById('record-renewal-time').value;
            const renewalAmount = document.getElementById('record-renewal-amount').value;
            const remark = document.getElementById('record-remark').value;
            const accumulatedAmount = document.getElementById('record-accumulated-amount').value;

            // 验证必填字段
            if (!date || !name || !amount || !monthlyAmount || !renewalTime || !renewalAmount) {
                showMessage('请填写所有必填字段', true);
                return;
            }

            const formData = new FormData();

            if (editingRecordId) {
                // 编辑模式
                formData.append('action', 'update_record');
                formData.append('record_id', editingRecordId);
            } else {
                // 添加模式
                formData.append('action', 'add_record');
                formData.append('book_id', currentBook.id);
            }

            formData.append('date', date);
            formData.append('name', name);
            formData.append('amount', amount);
            formData.append('monthly_amount', monthlyAmount);
            formData.append('renewal_time', renewalTime);
            formData.append('renewal_amount', renewalAmount);
            formData.append('remark', remark);

            // 编辑模式时包含累计金额
            if (editingRecordId && accumulatedAmount !== '') {
                formData.append('accumulated_amount', accumulatedAmount);
            }

            try {
                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideAddForm();
                    loadRecords();
                    showMessage(editingRecordId ? '记录更新成功！' : '记录添加成功！');
                } else {
                    showMessage(data.error || (editingRecordId ? '更新记录失败' : '添加记录失败'), true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error(editingRecordId ? '更新记录错误:' : '添加记录错误:', error);
            }
        }

        function resetRecordForm() {
            document.getElementById('record-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('record-name').value = '';
            document.getElementById('record-amount').value = '';
            document.getElementById('record-monthly-amount').value = '';
            document.getElementById('record-renewal-time').value = '';
            document.getElementById('record-renewal-amount').value = '';
            document.getElementById('record-remark').value = '';
            document.getElementById('record-accumulated-amount').value = '';
        }

        // 全局变量用于编辑
        let editingRecordId = null;



        function editRecord(recordId) {
            editingRecordId = recordId;
            const record = records.find(r => r.id == recordId);
            if (!record) {
                showMessage('记录不存在', true);
                return;
            }

            // 设置表单标题和显示累计金额
            document.getElementById('form-title').textContent = '编辑记录';
            document.getElementById('accumulated-amount-display').style.display = 'block';

            // 填充表单数据
            document.getElementById('record-date').value = record.date || '';
            document.getElementById('record-name').value = record.name || '';
            document.getElementById('record-amount').value = record.amount || '';
            document.getElementById('record-monthly-amount').value = record.monthly_amount || '';
            document.getElementById('record-renewal-time').value = record.renewal_time || '';
            document.getElementById('record-renewal-amount').value = record.renewal_amount || '';
            document.getElementById('record-remark').value = record.remark || '';
            document.getElementById('record-accumulated-amount').value = record.accumulated_amount || '';

            // 显示弹窗
            document.getElementById('add-form-modal').style.display = 'flex';
        }

        async function deleteRecord(recordId) {
            if (!confirm('确定要删除这条记录吗？删除后无法恢复。')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'delete_record');
                formData.append('record_id', recordId);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('记录删除成功！');
                    loadRecords();
                } else {
                    showMessage(data.error || '删除记录失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('删除记录错误:', error);
            }
        }

        function showChangePassword() {
            document.getElementById('change-password-modal').style.display = 'flex';
            // 清空表单
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
        }

        function hideChangePassword() {
            document.getElementById('change-password-modal').style.display = 'none';
        }

        async function changePassword(event) {
            event.preventDefault();

            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // 验证新密码和确认密码是否一致
            if (newPassword !== confirmPassword) {
                showMessage('新密码和确认密码不一致', true);
                return;
            }

            // 验证新密码长度
            if (newPassword.length < 6) {
                showMessage('新密码至少需要6位', true);
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'change_password');
                formData.append('current_password', currentPassword);
                formData.append('new_password', newPassword);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideChangePassword();
                    showMessage('密码修改成功！');
                } else {
                    showMessage(data.error || '密码修改失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('修改密码错误:', error);
            }
        }

        function logout() {
            localStorage.removeItem('token');
            token = '';
            currentBook = null;
            books = [];
            records = [];

            // 清除消息显示
            const messageEl = document.getElementById('message');
            messageEl.textContent = '';
            messageEl.style.display = 'none';

            // 重置表单
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';

            // 先隐藏主页面
            document.getElementById('main-page').style.display = 'none';

            // 强制重新设置登录页面的样式
            const loginPage = document.getElementById('login-page');
            loginPage.style.display = 'flex';
            loginPage.style.alignItems = 'center';
            loginPage.style.justifyContent = 'center';
            loginPage.style.minHeight = '100vh';
            loginPage.style.padding = '20px';

            // 重置页面滚动位置到顶部，确保登录界面居中显示
            window.scrollTo(0, 0);
            document.body.scrollTop = 0; // 兼容一些浏览器
            document.documentElement.scrollTop = 0; // 兼容一些浏览器
        }

        // 账本管理函数
        function showAddBookForm() {
            document.getElementById('add-book-modal').style.display = 'flex';
            // 清空表单
            document.getElementById('book-name').value = '';
            document.getElementById('book-description').value = '';
        }

        function hideAddBookForm() {
            document.getElementById('add-book-modal').style.display = 'none';
        }

        async function submitBook(event) {
            event.preventDefault();

            const name = document.getElementById('book-name').value;
            const description = document.getElementById('book-description').value;

            if (!name.trim()) {
                showMessage('请输入账本名称', true);
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'add_book');
                formData.append('name', name);
                formData.append('description', description);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideAddBookForm();
                    showMessage('账本创建成功！');
                    loadBooks(); // 重新加载账本列表
                } else {
                    showMessage(data.error || '创建账本失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('创建账本错误:', error);
            }
        }

        // 新增：下拉框悬浮body定位与关闭
        function showBookOptionsDropdown() {
            const customSelector = document.getElementById('custom-book-selector');
            const bookOptions = document.getElementById('book-options');
            if (!customSelector || !bookOptions) return;
            // 计算customSelector在页面中的位置
            const rect = customSelector.getBoundingClientRect();
            bookOptions.style.position = 'absolute';
            bookOptions.style.left = rect.left + 'px';
            bookOptions.style.top = (rect.bottom + window.scrollY) + 'px';
            bookOptions.style.width = rect.width + 'px';
            bookOptions.style.zIndex = 9999;
            bookOptions.style.display = 'block';
            // 移动到body下
            document.body.appendChild(bookOptions);
        }
        function hideBookOptionsDropdown() {
            const bookOptions = document.getElementById('book-options');
            if (bookOptions) {
                bookOptions.style.display = 'none';
            }
        }
    </script>
</body>
</html>
